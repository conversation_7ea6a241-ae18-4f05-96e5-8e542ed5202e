package com.ce.scrm.center.service.business;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.center.dao.entity.WebSiteCase;
import com.ce.scrm.center.dao.entity.WebSiteCaseItem;
import com.ce.scrm.center.dao.service.WebSiteCaseItemService;
import com.ce.scrm.center.dao.service.WebSiteCaseService;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * description: 案例推荐
 * <AUTHOR>
 * date: 2025/8/12.
 */
@Slf4j
@Service
public class WebSiteCaseBusiness {

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private WebSiteCaseService webSiteCaseService;

    @Resource
    private WebSiteCaseItemService webSiteCaseItemService;


    @Transactional(rollbackFor = Exception.class)
    public void createWebSiteCase(WebSiteCase webSiteCase) {
        String custId = webSiteCase.getCustomerId();
        if (StringUtils.isBlank(custId)) {
            log.warn("案例推荐查客户，参数客户id为空，webSiteCase={}", JSON.toJSONString(webSiteCase));
            return;
        }
        Optional<CustomerDataThirdView> customerDataThirdViewOptional = customerThirdService.getCustomerData(custId);
        CustomerDataThirdView customerDataThirdView = customerDataThirdViewOptional.orElse(null);
        if (Objects.isNull(customerDataThirdView)) {
            log.warn("案例推荐查客户，客户库无此客户，custId={}", custId);
            return;
        }

        // 1.案例推荐主表
        BeanUtils.copyProperties(customerDataThirdView, webSiteCase);
        webSiteCase.setId(null);
        webSiteCaseService.save(webSiteCase);
        //废弃flag
        //webSiteCaseService.lambdaUpdate()
        //        .setSql("flag = flag + 1")
        //        .eq(WebSiteCase::getCustomerId, custId)
        //        .update();
        // 2.案例推荐明细表（实例维度）：秦锋推送，此处不用写
    }

    /**
     * Description: 根据instanceCode更新WebSiteCaseItem
     * @author: JiuDD
     * @param webSiteCase
     * @return void
     * date: 2025/8/12 17:26
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWebSiteCaseItem(WebSiteCaseItem webSiteCase) {
        String instanceCode = webSiteCase.getInstanceCode();
        if (StringUtils.isBlank(instanceCode)) {
            return;
        }
        List<WebSiteCaseItem> instanceList = webSiteCaseItemService.list(
                new LambdaQueryWrapper<WebSiteCaseItem>()
                        .eq(WebSiteCaseItem::getInstanceCode, instanceCode)
                        .eq(WebSiteCaseItem::getFlag, 1)
        );
        if (CollectionUtils.isEmpty(instanceList)) {
            log.warn("根据instanceCode更新WebSiteCaseItem：查无此实例。instanceCode={}, webSiteCase={}", instanceCode, JSON.toJSONString(webSiteCase));
            return;
        }
        // 理论上flag=1的instanceCode只会有1条记录，如果有多条，就是大数据推送的数据有问题
        String customerId = instanceList.get(0).getCustomerId();
        if (!StringUtils.isBlank(customerId)) {
            // 该客户的所有案例
            List<WebSiteCaseItem> customerInstanceList = webSiteCaseItemService.list(
                    new LambdaQueryWrapper<WebSiteCaseItem>()
                            .eq(WebSiteCaseItem::getCustomerId, customerId)
                            .eq(WebSiteCaseItem::getFlag, 1)
            );
            log.info("customerInstanceList1={}", JSON.toJSONString(customerInstanceList));
            if (customerInstanceList.size() == 1) {
                // 该客户只有1个推荐案例时，直接 根据instanceCode更新 百度收录量 + 谷歌收录量 即可
                webSiteCaseItemService.update(webSiteCase, new LambdaQueryWrapper<WebSiteCaseItem>().eq(WebSiteCaseItem::getInstanceCode, instanceCode));
            } else if (customerInstanceList.size() > 1) {
                // 该客户只有多个推荐案例时，不仅需要 根据instanceCode更新当前实例的 百度收录量 + 谷歌收录量 ，还需要更新当前客户的最佳推荐analysisFlag字段。业务需求：同一个客户 language_version !='zh-CN'
                // 并且 google_include 最大的那一条的 analysisFlag 置1，其余置0。
                // 1. 将参数里的 百度收录量 + 谷歌收录量 更新到从数据库查询到的对应实例中，用于后面更新数据库该实例的收录量及计算该客户的analysisFlag
                customerInstanceList.stream().filter(item -> Objects.equals(item.getInstanceCode(), instanceCode)).findFirst().ifPresent(item -> {
                    item.setBaiduInclude(webSiteCase.getBaiduInclude());
                    item.setGoogleInclude(webSiteCase.getGoogleInclude());
                });
                log.info("customerInstanceList2={}", JSON.toJSONString(customerInstanceList));
                // 2. 更新analysisFlag，逻辑：同一个客户的language_version !='zh-CN' 并且 google_include 最大的那一条 置1，其余置0
                Optional<WebSiteCaseItem> maxGoogleIncludeItem = customerInstanceList.stream()
                        .filter(item -> !"zh-CN".equals(item.getLanguageVersion()))
                        .filter(item -> item.getGoogleInclude() != null && item.getGoogleInclude() > 0)
                        .max(Comparator.comparingInt(WebSiteCaseItem::getGoogleInclude));
                WebSiteCaseItem maxGoogleIncludeInstanceItem = maxGoogleIncludeItem.orElse(null);
                if (Objects.nonNull(maxGoogleIncludeInstanceItem)) {
                    maxGoogleIncludeInstanceItem.setAnalysisFlag(1);
                    // 其余analysisFlag置0
                    customerInstanceList.stream().filter(item -> !Objects.equals(item.getInstanceCode(), maxGoogleIncludeInstanceItem.getInstanceCode())).forEach(item -> item.setAnalysisFlag(0));
                    webSiteCaseItemService.updateBatchById(customerInstanceList);
                } else {
                    log.info("通过instanceCode={}找到的客户{}的非中文网站的“谷歌收录数”都为0", instanceCode, customerId);
                    // 如果外文网站都没有谷歌收录量，且已经有1个实例被设置为最佳案例，则只需更新当前案例的收录量即可
                    if (customerInstanceList.stream().anyMatch(item -> !"zh-CN".equals(item.getLanguageVersion()) && 1 == item.getAnalysisFlag())) {
                        // 根据instanceCode更新WebSiteCaseItem
                        webSiteCaseItemService.update(webSiteCase, new LambdaQueryWrapper<WebSiteCaseItem>().eq(WebSiteCaseItem::getInstanceCode, instanceCode));
                    } else {
                        // 如果没有任何一个外文网站的analysisFlag被设置为1，则随机挑选其中1个外文网站设置为最佳案例
                        customerInstanceList.stream().filter(item -> !"zh-CN".equals(item.getLanguageVersion())).findAny().ifPresent(item -> item.setAnalysisFlag(1));
                        webSiteCaseItemService.updateBatchById(customerInstanceList);
                    }
                }
            }
        }
    }
}
