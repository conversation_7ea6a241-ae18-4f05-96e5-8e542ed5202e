package com.ce.scrm.center.service.business;

import cn.ce.cecloud.business.entity.BusinessOpportunity;
import cn.ce.cecloud.business.entity.SjDetailDubboView;
import cn.ce.cecloud.business.service.BusinessAppService;
import cn.ce.cecloud.business.service.TelBusinessAppService;
import cn.ce.cecloud.business.vo.BusinessOpportunityVo;
import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.CustomerStageEnum;
import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.ce.cesupport.enums.favorites.ClueRuleEnum;
import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.framework.base.vo.Pagination;
import cn.ce.cesupport.newcustclue.service.ClueRuleAppService;
import cn.ce.cesupport.newcustclue.vo.ClueRuleVo;
import cn.ce.cesupport.sma.dto.CustomerStageInfoDto;
import cn.ce.cesupport.sma.service.CustomerStageAppService;
import cn.ce.cesupport.framework.base.vo.Pagination;
import cn.ce.cesupport.sma.service.SalerRoleAppService;
import cn.ce.cesupport.sma.service.SalesMajordomoAppService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.entity.dto.*;
import com.ce.scrm.center.service.business.entity.dto.org.OrgParentQueryBusinessDto;
import com.ce.scrm.center.service.business.entity.view.AreaBusinessOpportunityBusinessView;
import com.ce.scrm.center.service.business.entity.view.BatchResultBusinessView;
import com.ce.scrm.center.service.business.entity.view.org.OrgParentQueryBusinessView;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.enums.ConvertRelationEnum;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.OrgDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.service.third.invoke.SmaConvertLogThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

import static cn.ce.cesupport.enums.CodeMessageEnum.RPC_EXCEPTION;
import java.util.stream.Collectors;
import static com.ce.scrm.center.service.constant.Constants.phoneBlackList;

/**
 * @version 1.0
 * @Description: 商机相关
 * @Author: lijinpeng
 * @Date: 2024/12/26 15:00
 */
@Service
@Slf4j
public class BusinessOpportunityBusiness {

    @DubboReference
    private BusinessAppService businessAppService;

    @DubboReference
    private CustomerStageAppService customerStageAppService;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @DubboReference
    private ClueRuleAppService clueRuleAppService;

    @Resource
    private SmaConvertLogThirdService smaConvertLogThirdService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private OrgInfoBusiness orgInfoBusiness;
	@Resource
	private OrgThirdService orgThirdService;
	@Resource
	private EmployeeThirdService employeeThirdService;

    @DubboReference
    private TelBusinessAppService telBusinessAppService;

    @DubboReference
    private SalerRoleAppService salerRoleAppService;

    @DubboReference
    private SalesMajordomoAppService salesMajordomoAppService;

    /*
     * @Description 根据条件获取区域内的商机信息
     * <AUTHOR>
     * @date 2024/12/26 15:56
     * @param areaBusinessOpportunityBusinessDto
     * @return java.util.List<com.ce.scrm.center.service.business.entity.view.AreaBusinessOpportunityBusinessView>
     */
    public Pagination<AreaBusinessOpportunityBusinessView> getAreaBusinessOpportunityByCondition(AreaBusinessOpportunityBusinessDto areaBusinessOpportunityBusinessDto) {
        log.info("根据条件获取区域内的商机信息，参数为areaBusinessOpportunityBusinessDto={}", areaBusinessOpportunityBusinessDto);
        if(Objects.equals(areaBusinessOpportunityBusinessDto.getFirstDistributionAreaId(),"3950")) {
            log.error("跨境试图访问中企的区域商机列表!");
            throw new RuntimeException("没有权限访问!");
        }
        //创建分页
        Pagination<BusinessOpportunity> conditionDto = new Pagination<>();
//        conditionDto.setCurrentPage(areaBusinessOpportunityBusinessDto.getCurrentPage());
//        conditionDto.setPageSize(areaBusinessOpportunityBusinessDto.getPageSize());
        conditionDto.setCurrentPage(1);
        conditionDto.setPageSize(1000);
        Map<String, Object> params = new HashMap<>();
        params.put("firstDistributionAreaId", areaBusinessOpportunityBusinessDto.getFirstDistributionAreaId());
        params.put("firstDistributionSubId", areaBusinessOpportunityBusinessDto.getFirstDistributionSubId());
        params.put("startCreateTime", areaBusinessOpportunityBusinessDto.getStartCreateTime());
        params.put("endCreateTime", areaBusinessOpportunityBusinessDto.getEndCreateTime());
        conditionDto.setParams(params);

        Pagination<BusinessOpportunity> businessOpportunityPagination = businessAppService.selectPageByCondition(conditionDto);

        List<AreaBusinessOpportunityBusinessView> areaBusinessOpportunityBusinessViewList = BeanCopyUtils.convertToVoList(businessOpportunityPagination.getList(), AreaBusinessOpportunityBusinessView.class);
        Pagination<AreaBusinessOpportunityBusinessView> result = new Pagination<>();
        BeanUtils.copyProperties(businessOpportunityPagination, result);
//        result.setList(areaBusinessOpportunityBusinessViewList);

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            log.error("没有登录人");
            throw new RuntimeException("没有登录人");
        }
        String loginEmployeeId = currentUser.getId();
        List<AreaBusinessOpportunityBusinessView> resultList = new ArrayList<>();
	    List<String> salerIds = new ArrayList<>();
	    List<String> subIds = new ArrayList<>();
	    Map<String, CustProtectView> custProtectViewMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(areaBusinessOpportunityBusinessViewList)) {
			List<String> custIds = areaBusinessOpportunityBusinessViewList.stream()
				.map(AreaBusinessOpportunityBusinessView::getCustId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
			List<CustProtectView> custProtectViews = cmCustProtectService.selectByCustIds(custIds);
			if (CollectionUtils.isNotEmpty(custProtectViews)) {
				custProtectViewMap = custProtectViews.stream().collect(Collectors.toMap(CustProtectView::getCustId, view -> view));
			}
			subIds = custProtectViews.stream().map(CustProtectView::getSubcompanyId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
			salerIds = custProtectViews.stream().map(CustProtectView::getSalerId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
		}
	    Map<String, OrgDataThirdView> subIdMap = orgThirdService.getOrgData(subIds);
	    Map<String, EmployeeInfoThirdDto> salerIdMap = employeeThirdService.getEmployeeDataMap(salerIds);
        for (AreaBusinessOpportunityBusinessView item : areaBusinessOpportunityBusinessViewList) {
            //build客户状态
            try {
                CustomerStageInfoDto customerStageByCustomerName = customerStageAppService.getCustomerStageByCustomerName(item.getCustName(), loginEmployeeId);
                if(customerStageByCustomerName != null) {
                    cn.ce.cesupport.enums.CustomerStageEnum customerStageEnum = customerStageByCustomerName.getCustomerStageEnum();
                    if(Objects.equals(CustomerStageEnum.CLUE_POOL.getCode(),customerStageEnum.getCode()) || Objects.equals(CustomerStageEnum.CUST_POOL.getCode(),customerStageEnum.getCode())) {
                        item.setCustomerStateId(customerStageEnum.getCode());
                        item.setCustomerState(customerStageEnum.getName());
	                    CustProtectView custProtectView = custProtectViewMap.get(item.getCustId());
						if (custProtectView != null) {
		                    item.setLastReleaseTime(custProtectView.getUpdateTime());
							if (MapUtils.isNotEmpty(salerIdMap)) {
								String salerName = Optional.ofNullable(salerIdMap.get(custProtectView.getSalerId())).map(EmployeeInfoThirdDto::getName).orElse(StringUtils.EMPTY);
			                    item.setLastProtectSalerName(salerName);
							}
							if (MapUtils.isNotEmpty(subIdMap)) {
								String subName = Optional.ofNullable(subIdMap.get(custProtectView.getSubcompanyId())).map(OrgDataThirdView::getName).orElse(StringUtils.EMPTY);
								item.setLastProtectSubName(subName);
							}
						}
                        resultList.add(item);
                    }
                }
                if (resultList.size() == 100) {
                    break;
                }
            }catch (Exception e){
                log.info("处理失败，查找客户位置返回空，customerName={},loginEmployeeId={}", item.getCustName(),loginEmployeeId);
                throw new RuntimeException(e);
            }
        }
        result.setList(resultList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public BatchResultBusinessView anewAssignSjBatch(AnewAssignSjBatchBusinessDto anewAssignSjBatchBusinessDto) {

        log.info("anewAssignSjBatch，参数为anewAssignSjBatchBusinessDto={}", JSON.toJSONString(anewAssignSjBatchBusinessDto));

        EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
        if(currentUser == null) {
            throw new RuntimeException("当前登录人不存在");
        }

        List<String> sjCodeList = anewAssignSjBatchBusinessDto.getSjCodeList();
        String assignToSubId = anewAssignSjBatchBusinessDto.getAssignToSubId();
        Integer successCount = 0;
        Integer failCount = 0;

        List<BusinessOpportunity> bySjCodeList = businessAppService.findBySjCodeList(sjCodeList);

        if(CollectionUtils.isEmpty(bySjCodeList)) {
            log.error("商机表数据为空");
            return BatchResultBusinessView.builder().successCount(successCount).failCount(failCount).build();
        }

        for ( BusinessOpportunity businessOpportunity : bySjCodeList ) {

            Date currentDate = new Date();

            String custId = businessOpportunity.getCustId();
            if (StringUtils.isBlank(custId)) {
                failCount++;
                continue;
            }

            CmCustProtect cmCustProtect = cmCustProtectService.lambdaQuery().eq(CmCustProtect::getCustId, custId).one();
            if (cmCustProtect != null && !Objects.equals(cmCustProtect.getStatus(), ProtectStateEnum.CUSTOMER_POOL.getState())){
                failCount++;
                continue;
            }

            // 查询分配规则
            ClueRuleVo assignDateParam = new ClueRuleVo();
            assignDateParam.setSubId(assignToSubId);
            assignDateParam.setTypeCode(ClueRuleEnum.SJ_MAJORWILL_ASSIGN_EXCEED.getValue());
            ClueRuleVo assignDateResult = clueRuleAppService.getOneByClueRule(assignDateParam);
            if(assignDateResult == null || assignDateResult.getTypeValue() == null) {
                throw new RuntimeException("分配规则缺失");
            }

            OrgParentQueryBusinessView parentOrgByOrgId = orgInfoBusiness.getParentOrgByOrgId(OrgParentQueryBusinessDto.builder().orgId(assignToSubId).build());
            String assignToAreaId = parentOrgByOrgId.getAreaId();
            if(cmCustProtect == null) {
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
                CustomerDataThirdView customerDataThirdView = customerData.orElseThrow(() -> new ApiException(CodeMessageEnum.DATA_NOT_EXIST));
                cmCustProtect = new CmCustProtect();
                cmCustProtect.setCustId(custId);
                cmCustProtect.setCustName(customerDataThirdView.getCustomerName());
                cmCustProtect.setCreateTime(currentDate);
                cmCustProtect.setCustType(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
                cmCustProtect.setUncid(customerDataThirdView.getCertificateCode());

                cmCustProtect.setOccupy(YesOrNoEnum.YES.getCode()); // 不计算库容
                cmCustProtect.setSubcompanyId(assignToSubId);
                cmCustProtect.setAreaId(assignToAreaId);
                cmCustProtect.setStatus(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());
                cmCustProtect.setAssignTime(currentDate);
                cmCustProtect.setAssignDate(DateUtil.offsetDay(currentDate,assignDateResult.getTypeValue()));
                cmCustProtect.setAssignCustSource(AssignCustSourceSpecialEnum.SJ_ANEW_ASSIGN.getValue());
                cmCustProtect.setUpdateTime(currentDate);
                cmCustProtect.setUpdateBy(currentUser.getId());
                cmCustProtect.setCustomTags("");
                cmCustProtect.setBusioppoCode(null);
                cmCustProtectService.saveData(cmCustProtect);
            }else {
                cmCustProtect.setOccupy(YesOrNoEnum.YES.getCode()); // 不计算库容
                cmCustProtect.setSubcompanyId(assignToSubId);
                cmCustProtect.setAreaId(assignToAreaId);
                cmCustProtect.setStatus(ProtectStateEnum.MAJOR_WILL_ASSIGN.getState());
                cmCustProtect.setAssignTime(currentDate);
                cmCustProtect.setAssignDate(DateUtil.offsetDay(currentDate,assignDateResult.getTypeValue()));
                cmCustProtect.setAssignCustSource(AssignCustSourceSpecialEnum.SJ_ANEW_ASSIGN.getValue());
                cmCustProtect.setUpdateTime(currentDate);
                cmCustProtect.setUpdateBy(currentUser.getId());
                cmCustProtect.setCustomTags("");
                cmCustProtect.setBusioppoCode(null);
                cmCustProtectService.updateNullableByCustId(cmCustProtect);
            }

            Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
            if(customerData.isPresent()) {
                CustomerDataThirdView customerDataThirdView = customerData.get();
                ConvertLogBusinessDto build = ConvertLogBusinessDto.builder()
                        .custId(custId)
                        .subcompanyOfCurSalerId(assignToSubId)
                        .areaOfCurSalerId(assignToAreaId)
                        .createBy(currentUser.getId())
                        .createTime(currentDate)
                        .convertType(ConvertRelationEnum.SJ_ANEW_ASSIGN.getValue())
                        .custName(customerDataThirdView.getCustomerName())
                        .custType(customerDataThirdView.getProtectCustType() == null || customerDataThirdView.getProtectCustType().equals("null") ?
                                null : Integer.valueOf(customerDataThirdView.getProtectCustType()))
                        .entId(customerDataThirdView.getSourceDataId())//pid
                        .releaseReason(businessOpportunity.getBusiOppoCode())//备注
                        .build();
                smaConvertLogThirdService.insertLog(build);

                successCount++;
            }else {
                failCount++;
            }
        }

        return BatchResultBusinessView.builder().successCount(successCount).failCount(failCount).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean accept(@Valid AcceptBusinessDto acceptDto) {

        // 校验一下 跨境 && 是商务角色 && 已经回执 && 未选择 接收/不接受 && 不超过48小时
        BusinessOpportunityVo bo = telBusinessAppService.busiOppoById(acceptDto.getId());
//        Date assignTime = bo.getAssignTime();
//        Boolean flag = null;
//        if ( assignTime != null ) {
//            long fortyEightHoursMillis = TimeUnit.HOURS.toMinutes(48);
//            long timeDifference = System.currentTimeMillis() - assignTime.getTime();
//            flag = timeDifference > fortyEightHoursMillis;
//        }
        //  跨境 && 是商务角色 && 已经回执 && 未选择 接收/不接受 && 不超过48小时
//        if (!(PositionUtil.isKj(acceptDto.getLoginAreaId()) &&
//                PositionUtil.isBusinessSaler(acceptDto.getLoginPosition()) &&
//                Objects.equals(bo.getReceiptFlag(),2) &&
//                bo.getAcceptFlag() == null && assignTime != null && !flag)) {
//            throw new ApiException(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
//        }

        String opportunityHandleStatus = null;
        // 商机主表记录
        if (Objects.equals(acceptDto.getAcceptFlag(),YesOrNoEnum.YES.getCode())) {
            bo.setFollowFlag(YesOrNoEnum.YES.getCode());
            bo.setAcceptFlag(acceptDto.getAcceptFlag());
            bo.setFollowRate(acceptDto.getFollowRate());
            opportunityHandleStatus = "OPP_HANDLE_STATUS_12";
            bo.setHandleResult(opportunityHandleStatus);
        }else if (Objects.equals(acceptDto.getAcceptFlag(),YesOrNoEnum.NO.getCode())) {
            bo.setFollowFlag(YesOrNoEnum.YES.getCode());
            bo.setAcceptFlag(acceptDto.getAcceptFlag());
            bo.setWaiveExplain(acceptDto.getWaiveExplain());
            opportunityHandleStatus = "OPP_HANDLE_STATUS_13";
            bo.setHandleResult(opportunityHandleStatus);
        }
        Boolean b = telBusinessAppService.updateBusinessOpportunity(bo);
        if (!b) {
            throw new ApiException(RPC_EXCEPTION);
        }

        // 记录操作日志
        if (opportunityHandleStatus != null) {
            telBusinessAppService.addSjOperateLog(bo.getId(),bo.getBusiOppoCode(),bo.getStatus(),acceptDto.getLoginEmployeeId(),acceptDto.getLoginEmployeeName(),opportunityHandleStatus);
        }

        if (PositionUtil.checkSalerRole(acceptDto.getLoginPosition())) {
            // 如果是不接受 还需要释放此客户的保护关系
            if (Objects.equals(acceptDto.getAcceptFlag(),YesOrNoEnum.NO.getCode())) {
                try {
                    // DICT_RELEASE_REASON_001 = 客户无意向/无计划/无需求
                    salerRoleAppService.releaseMyCust(bo.getCustId(),acceptDto.getLoginEmployeeId(),"DICT_RELEASE_REASON_001",acceptDto.getWaiveExplain(),1);
                } catch (Exception e) {
                    log.error("48小时内不接受此商机，商务释放保护关系出错,acceptDto={}",JSON.toJSONString(acceptDto));
                    throw new RuntimeException(e);
                }
            }
        } else if (PositionUtil.isBusinessMajor(acceptDto.getLoginPosition())) {
            try {
                //DICT_RELEASE_REASON_SJ_003 =  其他
                salesMajordomoAppService.majorReleaseSjCust(acceptDto.getLoginEmployeeId(), acceptDto.getLoginSubId(), bo.getCustId(), "DICT_RELEASE_REASON_SJ_003");
            } catch (Exception e) {
                log.error("48小时内不接受此商机，总监释放保护关系出错,acceptDto={}",JSON.toJSONString(acceptDto));
                throw new RuntimeException(e);
            }
        }


        return Boolean.TRUE;
    }

    public String getPhoneNumber(QueryPhoneNumberBusinessDto queryDto) {

        if (phoneBlackList.contains(queryDto.getLoginEmployeeId())) {
            return null;
        }

        BusinessOpportunityVo query = new BusinessOpportunityVo();
        query.setId(queryDto.getBusiOppoId());
        query.setBusiOppoCode(queryDto.getSjCode());
        String mobile = telBusinessAppService.getMobile(query);

        return mobile;
    }

}
