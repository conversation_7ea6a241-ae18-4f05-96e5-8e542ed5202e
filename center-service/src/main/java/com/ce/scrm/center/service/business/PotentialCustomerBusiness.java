package com.ce.scrm.center.service.business;

import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.cma.vo.CustomerTag;
import cn.ce.cesupport.enums.CustSourceEnum;
import cn.ce.cesupport.enums.SalerGetclueFromEnum;
import cn.ce.cesupport.sma.constant.SjIntentConstant;
import cn.ce.cesupport.sma.vo.EmpCustSiteClockVo;
import cn.ce.cesupport.sma.vo.SjIntentInfoVo;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtectQuery;
import com.ce.scrm.center.dao.entity.CmCustProtectView;
import com.ce.scrm.center.dao.entity.SjIntentInfo;
import com.ce.scrm.center.dao.entity.SmaDictionaryItem;
import com.ce.scrm.center.dao.entity.view.CustProtectView;
import com.ce.scrm.center.dao.service.*;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.MyProtectCustomerBusinessDto;
import com.ce.scrm.center.service.business.entity.view.customer.MyProtectCustomerBusinessView;
import com.ce.scrm.center.service.business.entity.view.customer.MyProtectCustomerCapacityedCountBusinessView;
import com.ce.scrm.center.service.enums.ProtectCustTypeEnum;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.entity.view.SalerRoleDataThirdView;
import com.ce.scrm.center.service.third.invoke.*;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.util.date.DateUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 潜在客户业务
 * <AUTHOR>
 * @Date 2025-01-03 11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PotentialCustomerBusiness {

	private final CmCustProtectService cmCustProtectService;
	private final EmpCustSiteClockThirdService empCustSiteClockThirdService;
	private final CustomerThirdService customerThirdService;
	private final SmaDictionaryItemBusiness smaDictionaryItemBusiness;
	private final CmCustProtectViewService cmCustProtectViewService;
	private final OrgThirdService orgThirdService;
	private final EmployeeThirdService employeeThirdService;
	private final SmaSjIntentInfoService smaSjIntentInfoService;
	private final RecommendCustService recommendCustService;
	private final SmaDictionaryItemService smaDictionaryItemService;
	private final SalerRoleThirdService salerRoleThirdService;

	/**
	 * 我的保护列表
	 */
	public Page<MyProtectCustomerBusinessView> findMyProtectCustomer(MyProtectCustomerBusinessDto myProtectCustomerBusinessDto) {
		CmCustProtectQuery cmCustProtectQuery = buildCmCustProtectQuery(myProtectCustomerBusinessDto);
		Page<CustProtectView> custProtectViewPage = cmCustProtectService.selectCmCustProtectPage(cmCustProtectQuery);
		List<CustProtectView> records = custProtectViewPage.getRecords();
		if (CollectionUtils.isNotEmpty(records)) {
			List<MyProtectCustomerBusinessView> busResViewList = BeanCopyUtils.convertToVoList(records, MyProtectCustomerBusinessView.class);
			List<String> custIds = busResViewList.stream().map(MyProtectCustomerBusinessView::getCustId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
			List<String> orgIdList = new ArrayList<>();
			// 组织id集合
			busResViewList.forEach(vo -> {
					orgIdList.add(vo.getBussdeptId());
					orgIdList.add(vo.getSubcompanyId());
					orgIdList.add(vo.getAreaId());
			});
			Map<String, String> custIdStageMap = Optional.ofNullable(cmCustProtectViewService.lambdaQuery().in(CmCustProtectView::getCustId, custIds).list())
				.orElse(Collections.emptyList()).stream()
				.collect(Collectors.toMap(CmCustProtectView::getCustId, view -> StringUtils.isNotBlank(view.getSalesstage()) ? view.getSalesstage() : StringUtils.EMPTY));

			List<SjIntentInfo> sjIntentInfoList = smaSjIntentInfoService.selectListBySjCodes(
				busResViewList.stream().map(MyProtectCustomerBusinessView::getBusioppoCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
			Map<String, SjIntentInfo> sjIntentInfoMap= sjIntentInfoList.stream().collect(Collectors.toMap(SjIntentInfo::getSjCode, sjIntentInfo -> sjIntentInfo));

			busResViewList.forEach(vo -> {
				List<String> labels = new ArrayList<>();
				if (Objects.equals(vo.getCustType(), ProtectCustTypeEnum.COLLECTION.getValue())) {
					labels.add("预保护");
				}
				if (Objects.equals(vo.getCustSource(), CustSourceEnum.HAND.getValue())) {
					labels.add("自建客户");
				}
				// 最近一次打卡记录
				EmpCustSiteClockVo empCustSiteClockVo = empCustSiteClockThirdService.getCustSiteClockLastTime(vo.getSalerId(), vo.getCustId());
				vo.setLastSiteClockDate(Objects.nonNull(empCustSiteClockVo) ? empCustSiteClockVo.getCreateTime() : null);

				// 客户ID
				String custId = vo.getCustId();

				// 查询客户信息
				Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
				if (!customerData.isPresent()) {
					log.error("customer中未找到对应记录 客户信息不存在！ custId：{}",custId);
				}else{
					CustomerDataThirdView customerThirdView = customerData.get();
					// 覆盖客户名称
					vo.setCustName(customerThirdView.getCustomerName());
					vo.setKaFlag(customerThirdView.getTagKa());
					if (customerThirdView.getTagKa() != null && customerThirdView.getTagKa() == 1) {
						labels.add("KA");
					}
					vo.setRealNameAuthentication(customerThirdView.getRealNameAuthentication());
					// 门户实例相关标识
					List<CustomerTag> customerTags = customerThirdView.getCustomerTags();
					List<String> tagNames = Optional.ofNullable(customerTags).orElse(Lists.newArrayList()).stream().map(CustomerTag::getTagName).collect(Collectors.toList());
					labels.addAll(tagNames);
					log.info("custId ={} cache={}",custId, JSON.toJSONString(customerThirdView));
					String receiptSalerId = customerThirdView.getReceiptSalerId();
					if (StringUtils.isNotBlank(receiptSalerId)) {
						String salerId = vo.getSalerId();
						if (Objects.equals(receiptSalerId, salerId)) {
							// 判断 回执状态
							if (Objects.equals(customerThirdView.getReceiptFlag(),0)){
								// 待回执
								Date receiptEndTime = customerThirdView.getReceiptEndTime();
								if (Objects.nonNull(receiptEndTime)) {
									long betweenMs = DateUtil.betweenMs(new Date(), receiptEndTime);
									if (betweenMs > 0){
										// 转换为天、小时、分钟等格式
										String receiptCountdown = DateUtil.formatBetween(betweenMs, BetweenFormatter.Level.SECOND);
										vo.setRemainReceiptTime(receiptCountdown);
									}
								}
							}
						}
					}
				}
				// 销售阶段 枚举类？？？？？
				vo.setSalesStage(custIdStageMap.getOrDefault(custId, StringUtils.EMPTY));
				smaDictionaryItemBusiness.getById(vo.getSalesStage()).ifPresent(smaDictionaryItemView -> vo.setSalesStageStr(smaDictionaryItemView.getName()));

				String exceedTimeStr = DateUtils.formatSurplusDateZh(vo.getExceedTime());
				vo.setSurplusTime(exceedTimeStr);
				vo.setSurplusTimeRedOrNot(exceedTimeStr.contains("已过期"));
				if(vo.getCustSource() != null){
					CustSourceEnum custSourceEnumByValue = CustSourceEnum.of(vo.getCustSource());
					vo.setCustSourceStr(custSourceEnumByValue == null ? "" : custSourceEnumByValue.getLabel());
				}

				// 客户阶段
				Integer custTypeValue = vo.getCustType();
				if (custTypeValue != null) {
					ProtectCustTypeEnum custTypeEnum = ProtectCustTypeEnum.of(custTypeValue);
					if (custTypeEnum != null) {
						vo.setCustTypeStr(custTypeEnum.getLable());
					}

				}

				if(vo.getCustSourceSub() != null && Objects.equals(vo.getCustSourceSub(), SalerGetclueFromEnum.BIG_DATA_AI.getValue())){
					labels.add("机器人AI");
				}

				String busiOppoCode = vo.getBusioppoCode();
				// 如果为商机客户
				if (StringUtils.isNotBlank(busiOppoCode)) {
					SjIntentInfo sjIntentInfo = sjIntentInfoMap.get(busiOppoCode);
					if (sjIntentInfo != null) {
						labels.add("市场商机");
						SjIntentInfoVo sjIntentInfoVo = new SjIntentInfoVo();
						// 商机客户
						BeanUtils.copyProperties(sjIntentInfo, sjIntentInfoVo);

						if (sjIntentInfoVo.getIntentionalProducts() != null && !sjIntentInfoVo.getIntentionalProducts().equals("[]")) {
							List<Map> mapObjList = JSONObject.parseObject(sjIntentInfoVo.getIntentionalProducts(), List.class);
							StringBuffer s = new StringBuffer();
							for (Map map : mapObjList) {
								String productCodeLabel = (String) map.get("productCodeLabel");
								s.append(productCodeLabel).append(",");
							}
							s.deleteCharAt(s.length() - 1);
							sjIntentInfoVo.setIntentionalProducts(s.toString());
						}

						SmaDictionaryItem sjIndustryOneCodeDict = smaDictionaryItemService.getById(sjIntentInfoVo.getIndustryOneCode());
						sjIntentInfoVo.setIndustryOneCode(sjIndustryOneCodeDict == null ? "" : sjIndustryOneCodeDict.getName());

						String formatSurplusDateZh = DateUtils.formatSurplusDateZh(vo.getExceedTime());
						vo.setSurplusTime(formatSurplusDateZh);
						if(sjIntentInfoVo.getBusiOpporLabel() != null && !sjIntentInfoVo.getBusiOpporLabel().equals("[]")) {
							List<Map> mapObjList = JSONObject.parseObject(sjIntentInfoVo.getBusiOpporLabel(), List.class);
							StringBuffer s = new StringBuffer();
							for (Map map : mapObjList) {
								String label = (String) map.get("label");
								s.append(label).append(",");
							}
							s.deleteCharAt(s.length() - 1);
							sjIntentInfoVo.setBusiOpporLabel(s.toString());
						}

						// 设置商机状态
						String status = SjIntentConstant.getSjIntentStatusMap.get(sjIntentInfoVo.getStatus());
						sjIntentInfoVo.setStatus(status);
						Integer sjSource = sjIntentInfoVo.getSjSource();
						if(sjSource != null){
							SmaDictionaryItem dictionaryItem = smaDictionaryItemService.lambdaQuery()
								.eq(SmaDictionaryItem::getDictionaryId, "SJ_SOURCE")
								.eq(SmaDictionaryItem::getCode, sjSource.toString()).one();
							if(dictionaryItem != null){
								sjIntentInfoVo.setSjSourceLabel(dictionaryItem.getName());
							}
						}

						vo.setSjIntentInfoVo(sjIntentInfoVo);
						vo.setIsSj(1);
						vo.setSjSource(sjSource);
					} else { // 可能是搜客宝线索
						if(busiOppoCode.startsWith("SKB")) {
							labels.add("搜客宝同步");
							SjIntentInfoVo sjIntentInfoVo = new SjIntentInfoVo();
							sjIntentInfoVo.setSourceLabel("搜客宝线索");
							sjIntentInfoVo.setSjSource(4);
							vo.setSjIntentInfoVo(sjIntentInfoVo);
							vo.setIsSj(1);
							vo.setSjSource(4);
						}
					}
				}

				if(recommendCustService.getRecommendCustCount(vo.getCustId()) > 0) {
					labels.add("转介绍");
				}
				vo.setLabel(Joiner.on(",").join(labels));
			});

			// 批量获取员工和组织信息
			// 机构的map
			List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(orgIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
			Map<String, List<OrgThirdDto>> orgMap = Optional.ofNullable(orgThirdDtoList).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(OrgThirdDto::getId));


			List<String> empIds = busResViewList.stream().map(MyProtectCustomerBusinessView::getSalerId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
			Map<String, EmployeeInfoThirdDto> employeeMap = employeeThirdService.getEmployeeDataMap(empIds);

			// 部门、分司、区域的名称 设置
			busResViewList.forEach(record -> {
				String salerId = record.getSalerId();
				EmployeeInfoThirdDto emp = employeeMap.getOrDefault(salerId, null);
				record.setSalerName(Objects.nonNull(emp) ? emp.getName() : null);

				String currentDeptId = record.getBussdeptId();
				if (org.apache.commons.lang3.StringUtils.isNotBlank(currentDeptId)) {
					List<OrgThirdDto> currentDeptList  = orgMap.get(currentDeptId);
					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentDeptList)) {
						record.setDeptName(currentDeptList.get(0).getName());
					}
				}
				String currentSubId = record.getSubcompanyId();
				if (org.apache.commons.lang3.StringUtils.isNotBlank(currentSubId)) {
					List<OrgThirdDto> currentSubList  = orgMap.get(currentSubId);
					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentSubList)) {
						record.setSubName(currentSubList.get(0).getName());
					}
				}
				String currentAreaId = record.getAreaId();
				if (org.apache.commons.lang3.StringUtils.isNotBlank(currentAreaId)) {
					List<OrgThirdDto> currentAreaList  = orgMap.get(currentAreaId);
					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(currentAreaList)) {
						record.setAreaName(currentAreaList.get(0).getName());
					}
				}
			});

			Page<MyProtectCustomerBusinessView> page = Page.of(custProtectViewPage.getCurrent(), custProtectViewPage.getSize());
			page.setTotal(custProtectViewPage.getTotal());
			page.setPages(custProtectViewPage.getPages());
			page.setRecords(busResViewList);
			return page;
		}
		return Page.of(0L, 0L, 0L);
	}

	/**
	 * cmCustProtectService#selectCmCustProtectPage参数构建
	 */
	private CmCustProtectQuery buildCmCustProtectQuery(MyProtectCustomerBusinessDto businessDto) {

		if (businessDto == null) {
			return null;
		}
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		Assert.notNull(currentUser, "获取当前用户信息失败");
		CmCustProtectQuery cmCustProtectQuery = BeanCopyUtils.convertToVo(businessDto, CmCustProtectQuery.class);
		// custTypes
		ProtectCustTypeEnum custTypeEnum = Objects.nonNull(businessDto.getCustType()) ? ProtectCustTypeEnum.of(businessDto.getCustType()) : null;
		List<Integer> custTypes = new ArrayList<>();
		if (Objects.equals(ProtectCustTypeEnum.PROTECT_FOLLOW, custTypeEnum)) {
			custTypes.add(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
		} else if (Objects.equals(ProtectCustTypeEnum.COLLECTION, custTypeEnum)) {
			custTypes.add(ProtectCustTypeEnum.COLLECTION.getValue());
		} else {
			custTypes.add(ProtectCustTypeEnum.COLLECTION.getValue());
			custTypes.add(ProtectCustTypeEnum.PROTECT_FOLLOW.getValue());
		}
		cmCustProtectQuery.setCustTypes(custTypes);
		if (StringUtils.isNotBlank(businessDto.getCustSources())) {
			cmCustProtectQuery.setCustSources(Arrays.stream(businessDto.getCustSources().split(",")).filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList()));
		}
		// 保护时间
		if (StringUtils.isNotBlank(businessDto.getStartProtectDate()) && StringUtils.isNotBlank(businessDto.getEndProtectDate())) {
			cmCustProtectQuery.setStartProtectDate(businessDto.getStartProtectDate() + " 00:00:00");
			cmCustProtectQuery.setEndProtectDate(businessDto.getEndProtectDate() + " 23:59:59" + " 00:00:00");
		}
		cmCustProtectQuery.setFlag7s(businessDto.getTagFlag7());
		cmCustProtectQuery.setFlag8s(businessDto.getTagFlag8());
		cmCustProtectQuery.setCustomTags(businessDto.getCustomTags());
		cmCustProtectQuery.setBindFlag(businessDto.getBindFlag());
		cmCustProtectQuery.setQueryType(businessDto.getQueryType());

		// 职位条件
		if (PositionUtil.checkIsBusinessAreaZj(currentUser.getPosition())) {
			cmCustProtectQuery.setAreaId(currentUser.getAreaId());
			if (StringUtils.isNotBlank(businessDto.getSubId())) {
				cmCustProtectQuery.setSubcompanyId(businessDto.getSubId());
			}
			if (StringUtils.isNotBlank(businessDto.getBuId())) {
				cmCustProtectQuery.setBuId(businessDto.getBuId());
			}
			if (StringUtils.isNotBlank(businessDto.getDeptId())) {
				cmCustProtectQuery.setBussdeptId(businessDto.getDeptId());
			}
			if (StringUtils.isNotBlank(businessDto.getSalerId())) {
				cmCustProtectQuery.setSalerId(businessDto.getSalerId());
			}
		} else if (PositionUtil.isBusinessMajor(currentUser.getPosition())) {
			cmCustProtectQuery.setSubcompanyId(currentUser.getSubId());
			if (StringUtils.isNotBlank(businessDto.getBuId())) {
				cmCustProtectQuery.setBuId(businessDto.getBuId());
			}
			if (StringUtils.isNotBlank(businessDto.getDeptId())) {
				cmCustProtectQuery.setBussdeptId(businessDto.getDeptId());
			}
			if (StringUtils.isNotBlank(businessDto.getSalerId())) {
				cmCustProtectQuery.setSalerId(businessDto.getSalerId());
			}

		} else if (PositionUtil.checkSalerManagerDx(currentUser.getPosition())) {
			cmCustProtectQuery.setBussdeptId(currentUser.getOrgId());
			if (StringUtils.isNotBlank(businessDto.getSalerId())) {
				cmCustProtectQuery.setSalerId(businessDto.getSalerId());
			}
		} else if (PositionUtil.checkSalerDx(currentUser.getPosition()) && Objects.isNull(cmCustProtectQuery.getSalerId())) {
			cmCustProtectQuery.setSalerId(currentUser.getId());
		} else if (PositionUtil.isBusinessBu(currentUser.getPosition())) {
			cmCustProtectQuery.setBuId(currentUser.getBuId());
			if (StringUtils.isNotBlank(businessDto.getDeptId())) {
				cmCustProtectQuery.setBussdeptId(businessDto.getDeptId());
			}
			if (StringUtils.isNotBlank(businessDto.getSalerId())) {
				cmCustProtectQuery.setSalerId(businessDto.getSalerId());
			}
		} else {
			cmCustProtectQuery.setSalerId(currentUser.getId());
		}
		return cmCustProtectQuery;
	}

	/**
	 * 我的保护列表-库容信息
	 */
	public MyProtectCustomerCapacityedCountBusinessView myProtectCapacity() {
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		Assert.notNull(currentUser, "获取当前用户信息失败");
		boolean isSaler = PositionUtil.isBusinessSaler(currentUser.getPosition());
		if (!isSaler) {
			return MyProtectCustomerCapacityedCountBusinessView.builder().build();
		}
		String subId = currentUser.getSubId();
		String salerId = currentUser.getId();
		SalerRoleDataThirdView salerRoleDataThirdView = salerRoleThirdService.checkProtectRuleNums(subId, salerId);
		return BeanCopyUtils.convertToVo(salerRoleDataThirdView, MyProtectCustomerCapacityedCountBusinessView.class);
	}
}
