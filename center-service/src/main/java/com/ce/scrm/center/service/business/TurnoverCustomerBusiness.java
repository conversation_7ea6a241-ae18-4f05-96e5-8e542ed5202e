package com.ce.scrm.center.service.business;

import cn.ce.cecloud.base.utils.DateUtil;
import cn.ce.cesupport.base.exception.ApiException;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.CustOmsTemplateExceedStatusEnum;
import cn.ce.cesupport.enums.CustOmsTemplateFollowStatusEnum;
import cn.ce.cesupport.enums.CustOmsTemplateVisitStatusEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtectView;
import com.ce.scrm.center.dao.entity.CustOmsTemplate;
import com.ce.scrm.center.dao.entity.TurnoverCustOfOldCluePageQuery;
import com.ce.scrm.center.dao.service.CmCustProtectViewService;
import com.ce.scrm.center.dao.service.SmaCustOmsTemplateService;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.TurnoverFollowUpClueOldCustPageBusinessDto;
import com.ce.scrm.center.service.business.entity.view.customer.TurnoverCustomerOldCustPageBusinessView;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.dto.EmployeeInfoThirdDto;
import com.ce.scrm.center.service.third.entity.dto.OrgThirdDto;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.third.invoke.OrgThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 潜在客户业务
 * <AUTHOR>
 * @Date 2025-01-03 11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TurnoverCustomerBusiness {

	private final SmaCustOmsTemplateService smaCustOmsTemplateService;
	private final OrgThirdService orgThirdService;
	private final EmployeeThirdService employeeThirdService;
	private final BigDataThirdService bigDataThirdService;
	private final CmCustProtectViewService cmCustProtectViewService;
	private final SmaDictionaryItemBusiness smaDictionaryItemBusiness;

	/**
	 * {@link TurnoverCustomerBusiness#followUpClueForCustPage} 默认orderBy字段参数
	 */
	private static final  String DEFAULT_ORDER_BY_FIELD = "t.TEMPLATE_ISSUED_SUCCESS_TIME DESC";


	/**
	 * 成交客户-老客户线索跟进 列表
	 */
	public Page<TurnoverCustomerOldCustPageBusinessView> followUpClueForCustPage(TurnoverFollowUpClueOldCustPageBusinessDto followUpClueReqBusinessDto) {
		TurnoverCustOfOldCluePageQuery buildQueryParams = buildQueryParams(followUpClueReqBusinessDto);
		Page<CustOmsTemplate> custOmsTemplatePage = smaCustOmsTemplateService.queryFollowUpClueOfOldCust(buildQueryParams);
		List<CustOmsTemplate> records = custOmsTemplatePage.getRecords();
		Page<TurnoverCustomerOldCustPageBusinessView> resultPage = new Page<>();
		List<TurnoverCustomerOldCustPageBusinessView> resultList = BeanCopyUtils.convertToVoList(records, TurnoverCustomerOldCustPageBusinessView.class);
		resultPage.setRecords(resultList);
		if (CollectionUtils.isNotEmpty(resultList)) {
			resultPage.setRecords(pageFieldsProcessing(resultList));
			resultPage.setPages(custOmsTemplatePage.getPages());
			resultPage.setTotal(custOmsTemplatePage.getTotal());
			resultPage.setCurrent(custOmsTemplatePage.getCurrent());
			resultPage.setSize(custOmsTemplatePage.getSize());
			return resultPage;
		}
		return resultPage;
	}

	/**
	 * 设置商务名称、部门名称、分司名称
	 */
	private List<TurnoverCustomerOldCustPageBusinessView> pageFieldsProcessing(List<TurnoverCustomerOldCustPageBusinessView> resultList) {
		try {
			List<String> orgIdList = new ArrayList<>();
			resultList.forEach(item -> {
				orgIdList.add(item.getSubcompanyId());
				orgIdList.add(item.getBussdeptId());
			});
			// 为了获取 deptName、salerName、subName
			List<OrgThirdDto> orgThirdDtoList = orgThirdService.selectListByIds(orgIdList.stream().filter(
				StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
			Map<String, List<OrgThirdDto>> orgMap = Optional.ofNullable(orgThirdDtoList).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(OrgThirdDto::getId));
			List<String> empIds = resultList.stream().map(TurnoverCustomerOldCustPageBusinessView::getSalerId).filter(
				StringUtils::isNotBlank).distinct().collect(Collectors.toList());
			Map<String, EmployeeInfoThirdDto> employeeMap = employeeThirdService.getEmployeeDataMap(empIds);

			// 销售阶段字典、预计成交金额
			List<String> custIds = resultList.stream().map(TurnoverCustomerOldCustPageBusinessView::getCustId).filter(
				StringUtils::isNotBlank).distinct().collect(Collectors.toList());
			Map<String, CmCustProtectView> custIdViewMap = Optional.ofNullable(cmCustProtectViewService.lambdaQuery().in(
					CmCustProtectView::getCustId, custIds).list())
				.orElse(Collections.emptyList()).stream()
				.collect(Collectors.toMap(CmCustProtectView::getCustId, view -> view));

			Map<String, String> custIdItemMap = Optional.ofNullable(
					smaCustOmsTemplateService.lambdaQuery().in(CustOmsTemplate::getCustId, custIds)
						.orderByDesc(CustOmsTemplate::getTemplateIssuedSuccessTime).list()).orElse(Collections.emptyList())
				.stream().collect(Collectors.toMap(CustOmsTemplate::getCustId, o -> StringUtils.isNotBlank(o.getTemplateId()) ? o.getTemplateId() : StringUtils.EMPTY));

			resultList.forEach(item -> {
				// 分司、部门、销售 名称
				if (StringUtils.isNotBlank(item.getSubcompanyId())) {
					List<OrgThirdDto> currentSubList  = orgMap.get(item.getSubcompanyId());
					if (CollectionUtils.isNotEmpty(currentSubList)) {
						item.setSubName(currentSubList.get(0).getName());
					}
				}
				if (StringUtils.isNotBlank(item.getBussdeptId())) {
					List<OrgThirdDto> currentDeptList  = orgMap.get(item.getBussdeptId());
					if (CollectionUtils.isNotEmpty(currentDeptList)) {
						item.setDeptName(currentDeptList.get(0).getName());
					}
				}
				if (StringUtils.isNotBlank(item.getSalerId())) {
					EmployeeInfoThirdDto empInfo  = employeeMap.get(item.getSalerId());
					if (Objects.nonNull(empInfo)) {
						item.setSalerName(empInfo.getName());
					}
				}
				// 跟进状态
				Integer followOrNot = item.getFollowOrNot();
				if (followOrNot == null) {
					item.setFollowOrNotStr(CustOmsTemplateFollowStatusEnum.NOT_FOLLOWED.getName());
				} else {
					item.setFollowOrNotStr(CustOmsTemplateFollowStatusEnum.of(followOrNot).getName());
				}
				// 拜访状态
				Integer visitOrNot = item.getVisitOrNot();
				if (visitOrNot == null) {
					item.setVisitOrNotStr(CustOmsTemplateVisitStatusEnum.NOT_VISITED.getName());
				} else {
					item.setFollowOrNotStr(CustOmsTemplateFollowStatusEnum.of(visitOrNot).getName());
				}
				// 超期状态
				Integer isExceed = item.getIsExceed();
				if (isExceed == null) {
					item.setIsExceedStr(CustOmsTemplateExceedStatusEnum.NOT_EXCEED.getName());
				} else {
					item.setIsExceedStr(CustOmsTemplateExceedStatusEnum.of(isExceed).getName());
				}
				// 销售阶段、预计成交金额、线索跟进有效期
				CmCustProtectView cmCustProtectView = custIdViewMap.get(item.getCustId());
				if (cmCustProtectView != null) {
					smaDictionaryItemBusiness.getById(cmCustProtectView.getSalesstage()).ifPresent(smaDictionaryItemView -> item.setSalesStageStr(smaDictionaryItemView.getName()));
					BigDecimal expectDealAmount = cmCustProtectView.getExpectDealAmount();
					item.setExpectDealAmount(expectDealAmount);
					DecimalFormat moneyFormat = new DecimalFormat("#,##0.00");
					String expectDealAmountStr = moneyFormat.format(expectDealAmount) + "元";
					item.setExpectDealAmountStr(expectDealAmountStr);
					item.setClueFollowExpirationDate(cmCustProtectView.getClueFollowExpirationDate());
					item.setClueFollowExpirationDateStr(DateUtil.getDateStringH(cmCustProtectView.getClueFollowExpirationDate()));
				}
				item.setTemplateIssuedExceedTimeStr(DateUtil.getDateStringY(item.getTemplateIssuedExceedTime()));
				item.setTemplateIssuedSuccessTimeStr(DateUtil.getDateStringH(item.getTemplateIssuedSuccessTime()));
				if (custIdItemMap.get(item.getCustId()) == null) {
					item.setTemplateIds(custIdItemMap.values().stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
				} else {
					item.setTemplateIds(StringUtils.EMPTY);
				}
			});

			// 曾用名标记
			resultList.forEach(
				record ->
					record.setHistoryNameFlag(bigDataThirdService.getHistoryNameFlag(record.getCustName()))
			);
			return resultList;
		} catch (Exception e) {
			log.warn("TurnoverCustomerBusiness.pageFieldsProcessing error: {}", e.getMessage());
			throw new ApiException(CodeMessageEnum.FAILED);
		}
	}


	/**
	 * 成交客户-老客户线索跟进 查询参数构建
	 */
	private TurnoverCustOfOldCluePageQuery buildQueryParams(TurnoverFollowUpClueOldCustPageBusinessDto followUpClueReqBusinessDto) {
		EmployeeInfoBusinessDto currentUser = RouterContext.getCurrentUser();
		TurnoverCustOfOldCluePageQuery queryParams = BeanCopyUtils.convertToVo(followUpClueReqBusinessDto, TurnoverCustOfOldCluePageQuery.class);
		if (currentUser != null) {
			queryParams.setPageNum(followUpClueReqBusinessDto.getPageNum());
			queryParams.setPageSize(followUpClueReqBusinessDto.getPageSize());
			// 防止前端传空分页参数导致查询报错
			if (followUpClueReqBusinessDto.getPageNum() == null) {
				queryParams.setPageNum(1);
			}
			if (followUpClueReqBusinessDto.getPageSize() == null) {
				queryParams.setPageSize(10);
			}
			String position = currentUser.getPosition();

			String subId = currentUser.getSubId();
			String orgId = currentUser.getOrgId();
			String subIdSelected = followUpClueReqBusinessDto.getSubId();
			String deptIdSelected = followUpClueReqBusinessDto.getDeptId();
			String salerIdSelected = followUpClueReqBusinessDto.getSalerId();
			if (EmpPositionConstant.BUSINESS_MAJOR.equals(position)) {
				queryParams.setSubcompanyId(subId);
				queryParams.setBussdeptId(deptIdSelected);
				queryParams.setSalerId(salerIdSelected);
			} else if (PositionUtil.checkSalerManagerDx(position)) {
				queryParams.setBussdeptId(orgId);
				queryParams.setSalerId(salerIdSelected);
			} else if (PositionUtil.checkSalerDx(position)) {
				queryParams.setSalerId(currentUser.getId());
			} else if(EmpPositionConstant.BUSINESS_AREA.equals(position) || EmpPositionConstant.GJ_AREA.equals(position)) {
				queryParams.setAreaId(currentUser.getAreaId());
				queryParams.setSubcompanyId(subIdSelected);
				queryParams.setBussdeptId(deptIdSelected);
				queryParams.setSalerId(salerIdSelected);
			}
			queryParams.setOrderBy(StringUtils.isNotBlank(followUpClueReqBusinessDto.getOrderBy()) ? followUpClueReqBusinessDto.getOrderBy() : DEFAULT_ORDER_BY_FIELD);
			return queryParams;
		} else {
			throw new ApiException(CodeMessageEnum.NOT_LOGING_USER);
		}
	}
}
