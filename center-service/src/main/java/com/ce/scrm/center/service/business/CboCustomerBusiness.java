package com.ce.scrm.center.service.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.service.business.entity.dto.cbo.CboCreateCustomerBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cbo.CboSaveContactPersonBusinessDto;
import com.ce.scrm.center.service.business.entity.view.cbo.CboSaveContactPersonBusinessView;
import com.ce.scrm.center.service.business.entity.view.cbo.CboCreateCustomerBusinessView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.yml.ThirdCustomerConfig;
import com.ce.scrm.customer.dubbo.api.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 客户业务
 * <AUTHOR>
 * @date 2024/5/21 下午3:24
 * @version 1.0.0
 */
@Slf4j
@Service
public class CboCustomerBusiness {
    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private ThirdCustomerConfig thirdCustomerConfig;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo contactPersonDubbo;


    /**
     * Description: 跨境添加客户
     * @author: JiuDD
     * @param businessDto 客户参数
     * @return com.ce.scrm.center.service.business.entity.view.cbo.CboCreateCustomerBusinessView
     * date: 2025/6/23 19:58
     */
    public CboCreateCustomerBusinessView cboAddCustomer(CboCreateCustomerBusinessDto businessDto) {
        CboCreateCustomerBusinessView result = new CboCreateCustomerBusinessView();
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(businessDto.getCustomerName());
        if (customerDataByCustomerName.isPresent()) {
            log.warn("cbo创建客户失败，客户已存在，客户名称为:{}", businessDto.getCustomerName());
            result.setCustomerId(customerDataByCustomerName.get().getCustomerId());
            return result;
        }
        // 创建客户
        CustomerAddDubboDto customerAddDubboDto = BeanUtil.copyProperties(businessDto, CustomerAddDubboDto.class);
        this.setSignData(customerAddDubboDto);
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<CustomerAddDubboView> addDubboResult = customerDubbo.add(customerAddDubboDto);

        if (!addDubboResult.checkSuccess()) {
            log.warn("添加客户失败，调用客户dubbo异常，参数为为:{}，返回数据为:{}", JSON.toJSONString(businessDto), JSON.toJSONString(addDubboResult));
            result.setErrMsg(addDubboResult.getMsg());
            return result;
        }
        CustomerAddDubboView customerAddDubboView;
        String customerId;
        if ((customerAddDubboView = addDubboResult.getData()) == null || StrUtil.isBlank(customerId = customerAddDubboView.getCustomerId())) {
            log.warn("添加客户失败，参数为为:{}，返回数据为:{}", JSON.toJSONString(businessDto), JSON.toJSONString(addDubboResult));
            result.setErrMsg(addDubboResult.getMsg());
            return result;
        }
        result.setCustomerId(customerId);
        return result;
    }

    public CboSaveContactPersonBusinessView cboSaveContactPerson(CboSaveContactPersonBusinessDto businessDto) {
        CboSaveContactPersonBusinessView result = new CboSaveContactPersonBusinessView();
        ContactPersonAddDubboDto dubboDto = BeanUtil.copyProperties(businessDto, ContactPersonAddDubboDto.class);
        this.setSignData(dubboDto);
        DubboResult<ContactPersonAddDubboView> addDubboResult = contactPersonDubbo.add(dubboDto);
        if (!addDubboResult.checkSuccess()) {
            log.warn("添加联系人失败，调用联系人dubbo异常，参数为为:{}，返回数据为:{}", JSON.toJSONString(businessDto), JSON.toJSONString(addDubboResult));
            result.setErrMsg(addDubboResult.getMsg());
            return result;
        }
        ContactPersonAddDubboView contactPersonAddDubboView;
        String contactPersonId;
        if ((contactPersonAddDubboView = addDubboResult.getData()) == null || StrUtil.isBlank(contactPersonId = contactPersonAddDubboView.getContactPersonId())) {
            log.warn("添加联系人失败，参数为为:{}，返回数据为:{}", JSON.toJSONString(businessDto), JSON.toJSONString(addDubboResult));
            result.setErrMsg(addDubboResult.getMsg());
            return result;
        }
        result.setContactPersonId(contactPersonId);
        return result;
    }

    /**
     * 设置调用签名数据
     * @param signData  签名数据
     * <AUTHOR>
     * @date 2024/3/12 11:29
     **/
    private void setSignData(SignData signData) {
        signData.setSourceKey(thirdCustomerConfig.getKeyCbo());
        signData.setSourceSecret(thirdCustomerConfig.getSecretCbo());
    }
}