package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 根据custId查询同行业的未成交客户接口参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSameIndustryBusinessDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 每页数量
	 */
	private Integer pageSize = 10;

	/**
	 * custId 客户id
	 */
	private String custId;

	/**
	 * 行业等级：1跟入参的客户同一级行业，2跟入参的客户同二级行业，3跟入参的客户同三级行业，4跟入参的客户同四级行业
	 */
	private String sameIndustryLevel;

}
