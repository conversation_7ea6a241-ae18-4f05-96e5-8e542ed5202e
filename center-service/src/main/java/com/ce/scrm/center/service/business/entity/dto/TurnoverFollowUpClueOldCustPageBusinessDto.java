package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

/**
 * FavoritesPageBusinessDto
 *
 * <AUTHOR>
 * @since 2024/12/2 15:41
 */
@Data
public class TurnoverFollowUpClueOldCustPageBusinessDto {

	/**
	 * 页码
	 */
	private Integer pageNum;

	/**
	 * 每页数量
	 */
	private Integer pageSize;

	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 模板名称
	 */
	private String templateName;

	/**
	 * 是否跟进
	 */
	private Integer followOrNot;

	/**
	 * 是否拜访
	 */
	private Integer visitOrNot;

	/**
	 * 是否超期
	 */
	private Integer isExceed;

	/**
	 * 排序字段
	 */
	private String orderBy;

	/**
	 * 分司id
	 */
	private String subId;

	/**
	 * 部门id
	 */
	private String deptId;

	/**
	 * 商务id
	 */
	private String salerId;
}
