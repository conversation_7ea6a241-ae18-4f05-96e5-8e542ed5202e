package com.ce.scrm.center.service.business;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ImageDownload {

    static Dispatcher dispatcher = new Dispatcher();

    static {
        dispatcher.setMaxRequests(500);
        dispatcher.setMaxRequestsPerHost(500);
    }

    private static final OkHttpClient client = new OkHttpClient().newBuilder().dispatcher(dispatcher)
            .connectTimeout(10, TimeUnit.MINUTES)
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .connectionPool(new ConnectionPool(500, 10, TimeUnit.MINUTES))
            .build();

    @Recover
    public String recovers(ImageException e, String imageUrl, String savePath) {
        log.warn("下载图片返回异常, {}:", e.getMessage());
        log.warn("下载图片返回异常, JdConfigure{},{}", imageUrl, savePath);
        return null;
    }

    @Retryable(value = {ImageException.class}, maxAttempts = 1)
    public void downAndSaveImage(String imageUrl, String savePath) {
        Transaction t = Cat.newTransaction("service", "AI.ImageDownload.downAndSaveImage");
        t.setStatus(Transaction.SUCCESS);
        Request request = new Request.Builder()
                .url(imageUrl)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                log.info("图片保存位置：{}", savePath);
                saveImage(response.body().byteStream(), savePath);
            } else {
                String res = "";
                if (response.body() != null) {
                    res = new String(response.body().bytes());
                }
                log.warn("*************下载图片返回异常*************{},图片地址:{}", res, imageUrl);
                throw new ImageException("Failed to download image");
            }
        } catch (IOException e) {
            log.warn("图片下载失败", e);
            t.setStatus(e);
            Cat.logError(e);
            throw new ImageException("图片下载失败");
        } finally {
            t.complete();
        }
    }


    private void saveImage(InputStream inputStream, String filePath) throws IOException {
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    public class ImageException extends RuntimeException {
        public ImageException(String message) {
            super(message);
        }
    }
}
