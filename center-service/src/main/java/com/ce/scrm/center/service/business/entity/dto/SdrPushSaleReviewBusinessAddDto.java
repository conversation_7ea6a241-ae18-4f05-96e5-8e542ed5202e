package com.ce.scrm.center.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * SDR推送销售审核业务添加DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 */
@Data
public class SdrPushSaleReviewBusinessAddDto implements Serializable {

    /**
     * 审核来源类型 1：SDR 2：CC 必填非空
     */
    private Integer reviewSrcType;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份名称
     */
    private String provinceName;


    /**
     * 城市
     */
    private String city;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区
     */
    private String district;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 意向产品 支持多个
     */
    private String intentProduct;

    /**
     * 推荐理由
     */
    private String recommendedReason;

    /**
     * 附件信息
     */
    private String attachment;

    /**
     * 调配分司
     */
    private String assignSubId;

    /**
     * 备注原因
     */
    private String remarkReason;

    private String createdId;

    private String createdName;

} 