package com.ce.scrm.center.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.MessageRecord;
import com.ce.scrm.center.dao.entity.QyChatMessage;
import com.ce.scrm.center.dao.mapper.QyChatMessageMapper;
import com.ce.scrm.center.dao.service.MessageRecordService;
import com.ce.scrm.center.dao.service.QyChatMessageService;
import com.ce.scrm.center.service.business.entity.ChatDatas;
import com.ce.scrm.center.service.business.entity.EnterpriseParame;
import com.ce.scrm.center.service.business.entity.MsgContent;
import com.ce.scrm.center.service.business.entity.dto.QyChatPullDto;
import com.ce.scrm.center.service.utils.EnterperiseUtils;
import com.ce.scrm.center.service.utils.HttpUtils;
import com.ce.scrm.center.service.utils.RSAUtils;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.google.gson.Gson;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_QYWEHCAT_SEND_MESSAGE_TOPIC;

/**
 * @program: scrm-center
 * @ClassName QyChatBusiness
 * @description:
 * @author: lijinpeng
 * @create: 2025-07-17 09:51
 * @Version 1.0
 **/
@Slf4j
@Service
public class QyChatBusiness {

    private String filepath;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Resource
    private QyChatMessageService qyChatMessageService;

    @Autowired
    private MessageRecordService messageRecordService;

    public void pullMessage() {

        QyChatPullDto param = new QyChatPullDto();
        Integer seq = 0;// 从第几条开始拉取
//        Integer seq = qychatMapper.getSeq() == null ? 0 : qychatMapper.getSeq();// 从第几条开始拉取
        QyChatMessage one = qyChatMessageService.lambdaQuery().orderByDesc(QyChatMessage::getId).last("limit 1").one();
        if (one != null) {
            String seq1 = one.getSeq();
            seq = Integer.parseInt(seq1);
        }

        param.setLimit(100);// 一次拉取多少条消息 最大值为1000
        param.setTimeout(5);
        int ret = 0;
        long sdk = Finance.NewSdk();

        // 初始化
        Finance.Init(sdk, EnterpriseParame.CORPID, EnterpriseParame.SECRET);
        int limit = param.getLimit();
        long slice = Finance.NewSlice();
        ret = Finance.GetChatData(sdk, seq, limit, param.getProxy(), param.getPassword(), param.getTimeout(), slice);
        if (ret != 0) {
            log.error("getchatdata ret " + ret);
            return;
        }
        // 获取消息
        String data = Finance.GetContentFromSlice(slice);

        JSONObject jsonObject = JSONObject.parseObject(data);
        ChatDatas cdata = JSON.toJavaObject(jsonObject, ChatDatas.class);
        List<QyChatMessage> list = cdata.getChatdata();
        for (QyChatMessage qychat : list) {
            String msgs = qychat.getEncrypt_chat_msg();
            String encrypt_key = null;
            try {
                encrypt_key = RSAUtils.getPrivateKeyByPKCS1(qychat.getEncrypt_random_key());
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 将获取到的数据进行解密操作
            long msg = Finance.NewSlice();
            Finance.DecryptData(sdk, encrypt_key, msgs, msg);
            String decrypt_msg = Finance.GetContentFromSlice(msg);// 解密后的消息
//            log.info("第一个结果:qychat={}", JSON.toJSONString(qychat));
            qyChatMessageService.save(qychat);
            JSONObject content = JSONObject.parseObject(decrypt_msg);
            MsgContent msgcontent = new MsgContent();
            if(content.getString("action").equals("send"))
            {
                msgcontent.setMsgid(content.getString("msgid"));
                msgcontent.setAction(content.getString("action"));
                msgcontent.setFrom(content.getString("from"));
                msgcontent.setFromView(getUsernameByUserid(content.getString("from")));
//                msgcontent.setFromView(content.getString("from"));
                msgcontent.setTolist(content.getString("tolist"));
                msgcontent.setTolistView(getTolistByUserId(content.getString("tolist")));
//                msgcontent.setTolistView(content.getString("tolist"));
                msgcontent.setRoomid(content.getString("roomid"));
                msgcontent.setRoomidView(getGroupchatName(content.getString("roomid")));
//                msgcontent.setRoomidView(content.getString("roomid"));
                msgcontent.setMsgtime(content.getString("msgtime"));
                msgcontent.setMsgtype(content.getString("msgtype"));
                msgcontent.setText(isEmpty(content.getString("text")));
                msgcontent.setImage(isEmpty(content.getString("image")));
                msgcontent.setWeapp(isEmpty(content.getString("weapp")));
                msgcontent.setRedpacket(isEmpty(content.getString("redpacket")));
                msgcontent.setFile(isEmpty(content.getString("file")));
                msgcontent.setVideo(isEmpty(content.getString("video")));
                msgcontent.setVoice(isEmpty(content.getString("voice")));
                msgcontent.setChatrecord(isEmpty(content.getString("chatrecord")));
//                msgcontent.setFilename(getFileNameAndDownloadData(msgcontent)); // 暂时不要这个下载文件
            }else if(content.getString("action").equals("switch"))
            {
                log.info("switch 消息"+content);
                msgcontent.setMsgid(content.getString("msgid"));
                msgcontent.setAction(content.getString("action"));
                msgcontent.setFrom(content.getString("user"));
                msgcontent.setMsgtime(content.getString("time"));
                msgcontent.setMsgtype("switch");
                msgcontent.setFromView(getUsernameByUserid(content.getString("user")));
                msgcontent.setFromView(content.getString("user"));
            }else
            {
                msgcontent.setMsgid(content.getString("msgid"));
                msgcontent.setAction(content.getString("action"));
                msgcontent.setMsgtime(content.getString("time"));
                msgcontent.setMsgtype("revoke");
            }
            MessageRecord messageRecord = new MessageRecord();
            messageRecord.setMsgid(msgcontent.getMsgid());
            messageRecord.setAction(msgcontent.getAction());
            messageRecord.setFromUser(msgcontent.getFrom());
            messageRecord.setFromView(msgcontent.getFromView());
            messageRecord.setTolist(msgcontent.getTolist());
            messageRecord.setTolistView(msgcontent.getTolistView());
            messageRecord.setRoomid(msgcontent.getRoomid());
            messageRecord.setRoomidView(msgcontent.getRoomidView());
            messageRecord.setMsgtime(msgcontent.getMsgtime());
            messageRecord.setMsgtype(msgcontent.getMsgtype());
            messageRecord.setText(msgcontent.getText());
//            messageRecord.setImage(msgcontent.getImage());
//            messageRecord.setWeapp(msgcontent.getWeapp());
//            messageRecord.setRedpacket(msgcontent.getRedpacket());
//            messageRecord.setFile(msgcontent.getFile());
//            messageRecord.setVideo(msgcontent.getVideo());
//            messageRecord.setVoice(msgcontent.getVoice());
            messageRecord.setChatrecord(msgcontent.getChatrecord());
            messageRecord.setFilename(msgcontent.getFilename());
            messageRecord.setCreateTime(new Date());
            messageRecord.setUpdateTime(new Date());
            messageRecordService.save(messageRecord);
            // 解析消息 并插入到数据库
            String tolist = msgcontent.getTolist();
            log.info("发送的消息,msgcontent={}", JSON.toJSONString(msgcontent));
            //发送mq
            rocketMqOperate.syncSend(SCRM_QYWEHCAT_SEND_MESSAGE_TOPIC, JSON.toJSONString(messageRecord));
        }
        Finance.FreeSlice(slice);
        log.info("----------------------scheduled tasks qywx data success-----------------------");

    }

    private String content = null;

    private String isEmpty(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        } else {
            content = data;
            return data;

        }
    }

    private String getGroupchatName(String roomid) {
        if(StringUtils.isEmpty(roomid))
            return null;
        String data  = getGroupchatInfoByRoomid(roomid);
        JSONObject result = JSONObject.parseObject(data);
//        JsonObject result = gson.fromJson(data, JsonObject.class);
        String name=null;
        if(result.getInteger("errcode")!=0)
        {
            name = "该群不是客户群";
        }
        else
        {
            name = result.getJSONObject("group_chat").getString("name");
//            name  = result.get("group_chat").getAsJsonObject().get("name").getAsString();
        }

        return name;
    }

    public String getGroupchatInfoByRoomid(String roomid) {
        String accessToken = EnterperiseUtils.getAccessToken(EnterpriseParame.CORPID, EnterpriseParame.CUSTOMER_SECRET);
        Map<String, String> param = new HashMap<String, String>();
        param.put("chat_id", roomid);
        System.out.println(new Gson().toJson(param));
        String result = null;
        try {
            result = HttpUtils.post("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token="+accessToken,new Gson().toJson(param));
        } catch (Exception e) {
            log.error("获取客户群信息失败："+e);
        }
        log.info(result);
        return result;
    }

    private String getFileNameAndDownloadData(MsgContent msgcontent) {
        String fileName = null;
        try {
            String fileType = msgcontent.getMsgtype();
            JSONObject jsonObject = JSONObject.parseObject(content);
            String sdkfileid = jsonObject.getString("sdkfileid");
            fileName =getFileName(jsonObject,fileType);
            if (!StringUtils.isEmpty(sdkfileid) && null != fileName) {
                downLodaFile(fileName, sdkfileid);
            }
        } catch (Exception e) {
            log.info("下载文件出错" + e);
        }
        return fileName;

    }

    public void downLodaFile(String fileName, String sdkFileid) {
        int ret = 0;
        long sdk = Finance.NewSdk();
        // 初始化
        Finance.Init(sdk, EnterpriseParame.CORPID, EnterpriseParame.SECRET);
        String indexbuf = "";
        while (true) {
            long media_data = Finance.NewMediaData();
            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, null, null, 3, media_data);
            if (ret != 0) {
                return;
            }
//            System.out.printf("getmediadata outindex len:%d, data_len:%d, is_finis:%d\n",
//                    Finance.GetIndexLen(media_data), Finance.GetDataLen(media_data),
//                    Finance.IsMediaDataFinish(media_data));
            try {
                FileOutputStream outputStream = new FileOutputStream(new File(filepath + fileName), true);
                outputStream.write(Finance.GetData(media_data));
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Finance.IsMediaDataFinish(media_data) == 1) {
                Finance.FreeMediaData(media_data);
                break;
            } else {
                indexbuf = Finance.GetOutIndexBuf(media_data);
                Finance.FreeMediaData(media_data);
            }
        }
        log.info("下载完毕");
        Finance.DestroySdk(sdk);
    }

    private String getFileName(JSONObject jsonObject, String fileType) {
        String fileName = null;
        String md5sum =jsonObject.getString("md5sum");
        switch (fileType) {
            case "image":
                fileName = md5sum+ ".jpg";
                break;
            case "voice":
                fileName = md5sum+ ".mp3";
                break;
            case "video":
                fileName = md5sum+ ".mp4";
                break;
            case "file":
                fileName = jsonObject.getString("filename");
                break;
            default:
                fileName = "default.jpg";
                break;
        }
        return fileName;
    }

    public String getUsernameByUserid(String userId) {
        if(StringUtils.isEmpty(userId))
            return null;
        String userName = null;
        if (userId.startsWith("wb") || userId.startsWith("wo") || userId.startsWith("wm")) { // 外部联系人信息获取
            String data = getOuterCustomerDetails(userId);
            userName = analysisOuterCustomerData(data);
        } else { // 内部部联系人信息获取
            String data = getInnerCustomerDetails(userId);
            userName = analysisOuterCustomerData2(data);
        }
        return userName;

    }

    private String analysisOuterCustomerData2(String data) {
        String userName = null;
        JSONObject result = JSONObject.parseObject(data);
        if (result.getInteger("errcode") != 0) {
            log.info("解析异常 errcode" + result.getInteger("errcode"));
        } else {
            userName = result.getString("name");
        }
        return userName;
    }

    private String analysisOuterCustomerData(String data) {
        String userName = null;
        JSONObject result = JSONObject.parseObject(data);
        if (result.getInteger("errcode") != 0) {
            log.info("解析异常 errcode" + result.getInteger("errcode"));
        } else {
            userName = result.getJSONObject("external_contact").getString("name");
        }
        return userName;

    }

    public String getOuterCustomerDetails(String externalUserid) {
        String accessToken = EnterperiseUtils.getAccessToken(EnterpriseParame.CORPID, EnterpriseParame.CUSTOMER_SECRET);
        Map<String, String> param = new HashMap<String, String>();
        param.put("access_token", accessToken);
        param.put("external_userid", externalUserid);
        String data = null;
        try {
            data = HttpUtils.get("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get", param);
        } catch (Exception e) {
            log.error("获取外部客户信息失败："+e);
        }

        return data;

    }

    public String getInnerCustomerDetails(String userid) {
        String data = null;
        String accessToken = EnterperiseUtils.getAccessToken(EnterpriseParame.CORPID, EnterpriseParame.CUSTOMER_SECRET);
        Map<String, String> param = new HashMap<String, String>();
        param.put("access_token", accessToken);
        param.put("userid", userid);
        try {
            data = HttpUtils.get("https://qyapi.weixin.qq.com/cgi-bin/user/get", param);
        } catch (Exception e) {
            log.error("获取内部用户信息失败："+e);
        }
        return data;
    }

    private String getTolistByUserId(String tolist) {
        if(StringUtils.isEmpty(tolist))
            return null;
        List<String> list =new ArrayList<String>();
        JSONArray result = JSON.parseArray(tolist);
        for (int i = 0; i < result.size(); i++) {
            list.add(getUsernameByUserid(result.get(i).toString()));
        }
        return list.toString();
    }



}
