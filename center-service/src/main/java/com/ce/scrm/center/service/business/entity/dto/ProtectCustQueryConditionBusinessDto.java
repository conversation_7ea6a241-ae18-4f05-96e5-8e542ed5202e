package com.ce.scrm.center.service.business.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * Description: 保护关系表的客户查询条件
 * @author: JiuDD
 * date: 2025/5/8 15:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProtectCustQueryConditionBusinessDto implements Serializable {

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户id列表
     */
    private List<String> custIdList;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 事业部id
     */
    private String buId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    private Integer status;

    /**
     * custType 优先级高于 custTypeList
     */
    private Integer custType;
    /**
     * custTypeList 优先级低于 custType
     */
    private List<Integer> custTypeList;
    /**
     * custType not in
     */
    private List<Integer> custTypeNotInList;

    private List<String> subIdList;

    /**
     * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
     */
    private List<String> tagFlag7List;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

}
