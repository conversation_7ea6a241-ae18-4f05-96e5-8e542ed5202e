package com.ce.scrm.center.service.constant;

import com.ce.scrm.center.cache.constant.CacheConstant;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 16:21
 **/
@Slf4j
public class ServiceConstant {

    /**
     * 精确查询标记
     */
    public final static ThreadLocal<Boolean> EXACT_QUERY_FLAG = new ThreadLocal<>();


    public static class CacheKey {
        /**
         * demo缓存
         */
        public final static String DEMO_CACHE = CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + "demo";
        public final static String TRADE_PRODUCT = CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + "TRADE:PRODUCT:CODECACHE:";


    }

    /**
     * 锁key
     */
    public static class LockKey {
        /**
         * demo锁前缀
         */
        public final static String DEMO_LOCK = "demo";


        public final static String AICRM_IMAGE_GENERATOR_LIMIT = "ai:crm:image:generatorlimit";
    }

    /**
     * 定时任务相关常量
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2023/4/6 16:22
     **/
    public static class JobConstant {

        /**
         * 任务类型常量
         */
        public static class JobDescConstant {
            /**
             * 示例描述
             */
            public static final String DEMO_DESC = "当前任务是一个示例";

            public static final String SEGMENT_DISTRIBUTE_PREFIX = "分群任务ID:";

            /**
             * ABM 下发商务定时回执检查
             */
            public static final String SCRM_ABM_RECEIPT_CHECK_JOB_DESC = "ABM商机下发后回执检查:";
            public static final String SCRM_ABM_RECEIPT_REMIND_JOB_DESC = "ABM商机下发后回执提醒:";
            public static final String SCRM_ABM_CUST_FOLLOW_CHECK_JOB_DESC = "ABM商机下发后跟进检查:";
        }

        /**
         * 任务处理器
         */
        public static class JobHandlerConstant {
            /**
             * 清洗客户数据
             */
            public static final String CLEAN_CUSTOMER_JOB_HANDLER = "CLEAN_CUSTOMER_JOB_HANDLER";
            /**
             * 停止清洗客户数据
             */
            public static final String STOP_CLEAN_CUSTOMER_JOB_HANDLER = "STOP_CLEAN_CUSTOMER_JOB_HANDLER";
            /**
             * 迁移无效客户数据
             */
            public static final String TRANSFER_INVALID_CUSTOMER_JOB_HANDLER = "TRANSFER_INVALID_CUSTOMER_JOB_HANDLER";
            /**
             * 刷新客户名称和uncid
             */
            public static final String REFRESH_CUSTOMER_NAME_JOB_HANDLER = "REFRESH_CUSTOMER_NAME_JOB_HANDLER";
            /**
             * 定时给商务发送即将流失的微信消息
             */
            public static final String SCRM_WX_WILL_LOSS_JOB_HANDLER = "SCRM_WX_WILL_LOSS_JOB_HANDLER";
            /**
             * 定时给商务发送即将流失的微信消息
             */
            public static final String SCRM_WX_WILL_CIRCULATION_JOB_HANDLER = "SCRM_WX_WILL_CIRCULATION_JOB_HANDLER";
            /**
             * 客户超期到客户池和到总监待分配
             */
            public static final String SCRM_WILL_LOSS_POOL_MAJOR_JOB_HANDLER = "SCRM_WILL_LOSS_POOL_MAJOR_JOB_HANDLER";

            public static final String SYNC_CUSTOMER_KA_FLAG_JOB_HANDLER = "SYNC_CUSTOMER_KA_FLAG_JOB_HANDLER";
            /**
             * customer_will_circulation id放入MQ，用于待流转客户流转条件检查，及自动流转客户
             */
            public static final String CUSTOMER_WILL_CIRCULATION_ID_TO_MQ_FOR_CHECK = "CUSTOMER_WILL_CIRCULATION_ID_TO_MQ_FOR_CHECK";
            /**
             * 每日物理删除10天前的待流转客户
             */
            public static final String CLEAR_EXPIRED_CUSTOMER_WILL_CIRCULATION = "CLEAR_EXPIRED_CUSTOMER_WILL_CIRCULATION";
            /**
             * 定时任务发送总监微信未处理的待分配消息
             */
            public static final String SCRM_MAJOR_SEND_WX_UNHANDLED_MSG_JOB_HANDLER = "SCRM_MAJOR_SEND_WX_UNHANDLED_MSG_JOB_HANDLER";

            /**
             * 定时任务：经理待分配（线索、商机、未成交、成交）给经理发企微提醒
             */
            public static final String SCRM_MANAGER_SEND_WX_UNHANDLED_MSG_JOB_HANDLER = "SCRM_MANAGER_SEND_WX_UNHANDLED_MSG_JOB_HANDLER";
            /**
             * 每年获取节假日定时任务
             */
            public static final String SCRM_GET_HOLIDAY_JOB_HANDLER = "SCRM_GET_HOLIDAY_JOB_HANDLER";

            /**
             * 系统自动处理调拨申请
             */
            public static final String SCRM_TRANSFER_AUTO_PASS_JOB_HANDLER = "SCRM_TRANSFER_AUTO_PASS_JOB_HANDLER";

            /**
             * 合作商务约定时间剩余七天提醒
             */
            public static final String SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND = "SCRM_COOPERATION_PERIOD_VALIDITY_SEVEN_DAY_REMIND";
            public static final String SCRM_EQIXIU_JOB_HANDLER = "SCRM_EQIXIU_JOB_HANDLER";

            public static final String SCRM_SEGMENT_DISTRIBUTE_JOB_HANDLER = "SCRM_SEGMENT_DISTRIBUTE_JOB_HANDLER";

            /**
             * 商机48小时内提醒确认
             */
            public static final String SCRM_SJ_REMIND_JOB_HANDLER = "SCRM_SJ_REMIND_JOB_HANDLER";

            /**
             * 商机超过48小时自动确认逻辑
             */
            public static final String SCRM_SJ_AUTOMATIC_CONFIRMATION_JOB_HANDLER = "SCRM_SJ_AUTOMATIC_CONFIRMATION_JOB_HANDLER";

            public static final String TEST_1 = "TEST_1";

            /**
             * 计算 中企跟进记录按照销售阶段客户数汇总表
             * 每小时计算一次
             * 总监工作台展示需要
             */
            public static final String SCRM_ZQ_CUSTOMER_FOLLOW_STAGE_SUMMARY_JOB_HANDLER = "SCRM_ZQ_CUSTOMER_FOLLOW_STAGE_SUMMARY_JOB_HANDLER";

            /**
             * 计算 业绩预测汇总表
             * 每小时计算一次
             * 总监工作台展示需要
             */
            public static final String SCRM_ZQ_PERFORMANCE_PREDICT_SUMMARY_JOB_HANDLER = "SCRM_ZQ_PERFORMANCE_PREDICT_SUMMARY_JOB_HANDLER";

            /**
             * 中企跟进记录按照销售阶段客户数汇总表 和 业绩预测汇总表 月结
             */
            public static final String SCRM_ZQ_CUSTOMER_MONTH_SUMMARY_JOB_HANDLER = "SCRM_ZQ_CUSTOMER_MONTH_SUMMARY_JOB_HANDLER";

            /**
             * 企业微信拉取消息定时任务
             */
            public static final String SCRM_QYCHAT_MSG_JOB_HANDLER = "SCRM_QYCHAT_MSG_JOB_HANDLER";

            /**
             * 每小时一次 查询三天未确认保护关系的sj客户
             */
            public static final String SCRM_SJ_THREE_DAY_UNCONFIRMED_PROTECT_JOB_HANDLER = "SCRM_SJ_THREE_DAY_UNCONFIRMED_PROTECT_JOB_HANDLER";

            /**
             * ABM 下发商务定时回执检查
             */
            public static final String SCRM_ABM_RECEIPT_CHECK_JOB_HANDLER = "SCRM_ABM_RECEIPT_CHECK_JOB_HANDLER";
            public static final String SCRM_ABM_RECEIPT_REMIND_JOB_HANDLER = "SCRM_ABM_RECEIPT_REMIND_JOB_HANDLER";
            public static final String SCRM_ABM_CUST_FOLLOW_CHECK_JOB_HANDLER = "SCRM_ABM_CUST_FOLLOW_CHECK_JOB_HANDLER";

            /**
             * 3小时一次 缓存一下 组织机构员工树
             */
            public static final String SCRM_ORG_EMPLOYEE_TREE_JOB_HANDLER = "SCRM_ORG_EMPLOYEE_TREE_JOB_HANDLER";

            /*
             * 定时清除过期的推荐案例客户
             */
            public static final String WEB_SITE_CASE_CLEAR_JOB_HANDLER = "WEB_SITE_CASE_CLEAR_JOB_HANDLER";

        }
    }

    /**
     * mq常量池
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2023/4/6 16:23
     **/
    public static class MqConstant {

        /**
         * 主题标签分隔符
         */
        public final static String TOPIC_TAG_SEPARATOR = ":";

        /**
         * 主题
         */
        public static class Topic {
            /**
             * 客户相关topic
             */
            public final static String CUSTOMER_TOPIC = "CUSTOMER_TOPIC";

            /**
             * 刷新客户名称和unCid topic
             */
            public final static String CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC = "CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC";
            /**
             * 搜客宝相关标签同步
             */
            public final static String CDP_CUS_FLAG_TOPIC = "CDP_CUS_FLAG_TOPIC";
            /**
             * 门户实例标签同步
             */
            public final static String CDP_CUSTOMER_TAG_UPDATE_TOPIC = "CDP_CUSTOMER_TAG_UPDATE_TOPIC";
            /**
             * 客户信息放入MQ，用于去搜客宝es查询flag值
             */
            public final static String CDP_CUS_INFO_FOR_FLAG_TOPIC = "CDP_CUS_INFO_FOR_FLAG_TOPIC";
            /**
             * 发送微信消息
             */
            public final static String CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC = "CESUPPORT_SCRM_WECHAT_MESSAGE_SEND_TOPIC";

            /**
             * 每日7点刷客户数据
             */
            public final static String CDP_HISTORY_CUS_ID_FOR_CDP_TOPIC = "CDP_HISTORY_CUS_ID_FOR_CDP_TOPIC";
            /**
             * 客户id放入MQ，用于将待流转客户insert到待流转表
             */
            public final static String CUST_ID_FOR_CIRCULATION_TOPIC = "CUST_ID_FOR_CIRCULATION_TOPIC";

            /**
             * 监听CDP_HISTORY_CUS_ID_FOR_CDP_TOPIC，包装消息给成交客户保有/流失业务
             */
            public final static String CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_TOPIC = "CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_TOPIC";
            public final static String CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_AI_CHECK_TOPIC = "CESUPPORT_SCRM_CUSTOMER_KEEP_AND_LOSS_AI_CHECK_TOPIC";

            /**
             * 同步修改customer 客户表中的当前客户阶段字段
             */
            public final static String CESUPPORT_SCRM_PRESENT_STAGE_TOPIC = "CESUPPORT_SCRM_PRESENT_STAGE_TOPIC";

            /**
             * 异步同步状态给大数据topic
             */
            public final static String CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC = "CESUPPORT_SCRM_BIGDATA_FLAG_TOPIC";

            /**
             * 收藏成功的客户的联系人放入缓存中
             */
            public final static String CESUPPORT_SCRM_CONTACTPERSON_TO_REDIS_TOPIC = "CESUPPORT_SCRM_CONTACTPERSON_TO_REDIS_TOPIC";

            /**
             * 保护关系表变更
             */
            public final static String CDP_CM_CUST_PROTECT_TOPIC = "CDP_CM_CUST_PROTECT_TOPIC";
            public final static String CDP_AI_ROBOT_TOPIC = "CDP_AI_ROBOT_TOPIC";
            /**
             * 监听待流转流失表的binlog
             */
            public final static String CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_TOPIC = "CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_TOPIC";
            /**
             * 待流转流失表的id
             */
            public final static String CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_ID_TOPIC = "CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_ID_TOPIC";
            /**
             * 企业微信审批（回调到中企平台然后发MQ，我们监听）
             */
            public final static String SCRM_COOPERATION_APPROVE_TOPIC = "SCRM_COOPERATION_APPROVE_TOPIC";

            /**
             * 易企秀消息监听
             */
            public final static String SCRM_EQIXIU_MESSAGE_NOTIFY_TOPIC = "SCRM_EQIXIU_MESSAGE_NOTIFY_TOPIC";
            public final static String SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_TOPIC = "SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_TOPIC";

            /**
             * 三方活动上报
             */
            public final static String SCRM_CAMPAIGN_REPORT_TOPIC = "SCRM_CAMPAIGN_REPORT_TOPIC";

            /**
             * 分群下发topic
             */
            public final static String CDP_SEGMENT_CUSTOMER_INFO_TOPIC = "CDP_GROUP_CUSTOMER_INFO_TOPIC";

            /**
             * cdp客戶分群中客户信息（案例分享）
             */
            public static final String CDP_GROUP_WEB_SITE_CASE_TOPIC ="CDP_GROUP_WEB_SITE_CASE_TOPIC";

            /**
             * 门户推送的网站收录数据（案例分享）
             */
            public static final String SCRM_WEBSITE_INCLUDE_TOPIC ="SCRM_WEBSITE_INCLUDE_TOPIC";

            public final static String CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_TOPIC = "CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_TOPIC";


            /**
             * 分群信息topic
             */
            public final static String CDP_CUSTOMER_GROUP_ID_TOPIC = "CDP_CUSTOMER_GROUP_ID_TOPIC";

            /**
             * 商务拜访记录binlog
             */
            public static final String CESUPPORT_CRM_SITE_CLOCK_TOPIC = "CESUPPORT_CRM_SITE_CLOCK_TOPIC";

            public final static String SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC = "SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_TOPIC";
            public final static String SCRM_VOICE_AI_NOTIFY_TOPIC = "SCRM_VOICE_AI_NOTIFY_TOPIC";
            public final static String SCRM_AI_SPEECH_RECOGNITION_TOPIC = "SCRM_SPEECH_RECOGNITION_TOPIC";
            public final static String SCRM_VOICE_AI_NOTIFY_FOLLOW_TOPIC = "SCRM_VOICE_AI_NOTIFY_FOLLOW_TOPIC";
            public final static String SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC = "SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_TOPIC";
            public final static String CRM_AUDIO_COMBINE_URL_TOPIC = "CRM_AUDIO_COMBINE_URL_TOPIC";
            public final static String CRM_AUDIO_COMBINE_URL_UPDATE_TOPIC = "CRM_AUDIO_COMBINE_URL_UPDATE_TOPIC";
            public final static String SCRM_AI_IMAGE_GENERATOR_TOPIC = "SCRM_AI_IMAGE_GENERATOR_TOPIC";
            public final static String SCRM_AI_WEBSITE_GENERATOR_TOPIC = "SCRM_AI_WEBSITE_GENERATOR_TOPIC";


            /**
             * cdp分群下发 添加收藏
             */
            public static final String CDP_SEGMENT_GROUP_ADD_FAVORITE_TOPIC ="CDP_SEGMENT_GROUP_ADD_FAVORITE_TOPIC";

            /**
             * cdp分群下发 分配动作
             */
            public static final String CDP_SEGMENT_GROUP_ASSIGNMENT_TOPIC ="CDP_SEGMENT_GROUP_ASSIGNMENT_TOPIC";

            /**
             * cdp分群下发 商务标注有价值、无价值
             */
            public static final String CDP_SEGMENT_GROUP_SALER_TAG_TOPIC ="CDP_SEGMENT_GROUP_SALER_TAG_TOPIC";



            public static final String SCRM_OPERATION_LOG_TOPIC ="SCRM_OPERATION_LOG_TOPIC";

            /**
             * 业务平台 员工异动
             */
            public static final String CESUPPORT_GLOBAL_EMP_CHANGE_TOPIC ="CESUPPORT_GLOBAL_EMP_CHANGE_TOPIC";

            /**
             * customerId 批量发送 （流转在用）
             */
            public final static String SCRM_CUSTOMER_ID_BATCH_SEND_TOPIC = "SCRM_CUSTOMER_ID_BATCH_SEND_TOPIC";

            /**
             * customer_circulation表binlog
             */
            public final static String SCRM_CUSTOMER_CIRCULATION_BINLOG_TOPIC = "SCRM_CUSTOMER_CIRCULATION_BINLOG_TOPIC";

            /**
             * 客户表binlog
             */
            public final static String CDP_CUSTOMER_TOPIC = "CDP_CUSTOMER_TOPIC";

            public final static String CESUPPORT_SCRM_TRADE_PRODUCT_AI_TOPIC = "CESUPPORT_SCRM_TRADE_PRODUCT_AI_TOPIC";


            /**
             * cdp同步customer表的topic
             */
            public final static String CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC = "CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC";

            /**
             * 分群下发动作事件
             */
            public final static String CDP_SEGMENT_DISTRIBUTE_TOPIC = "CDP_SEGMENT_DISTRIBUTE_TOPIC";
            public final static String SCRM_WEB_SITE_CASE_ITEM_TOPIC = "SCRM_WEB_SITE_CASE_ITEM_TOPIC";

            /**
             * 企业微信 会话存档
             */
            public final static String SCRM_QYWEHCAT_SEND_MESSAGE_TOPIC = "SCRM_QYWEHCAT_SEND_MESSAGE_TOPIC";
            public final static String CRM_ROBOT_MESSAGE_RESOLVE_TOPIC = "CRM_ROBOT_MESSAGE_RESOLVE_TOPIC";

            /**
             * abm 审核通过
             */
            public final static String CRM_ABM_REVIEW_PASS_TOPIC = "CRM_ABM_REVIEW_PASS_TOPIC";

	        /**
	         * CRM 现有渠道 leads导入
	         */
	        public final static String SCRM_SEND_CLUE_NEW_TOPIC = "SCRM_SEND_CLUE_NEW_TOPIC";

            /**
             * abm 修改客户名导致的客户信息合并
             */
            public final static String CRM_ABM_MERGE_CUSTOMER_INFO_TOPIC = "CRM_ABM_MERGE_CUSTOMER_INFO_TOPIC";
        }

        /**
         * 标签
         */
        public static class Tag {
            /**
             * 检查有效客户标签
             */
            public final static String CHECK_VALID_CUSTOMER_TAG = "CHECK_VALID_CUSTOMER_TAG";
            /**
             * 迁移无效客户标签
             */
            public final static String TRANSFER_INVALID_CUSTOMER_TAG = "TRANSFER_INVALID_CUSTOMER_TAG";
        }

        /**
         * 订阅组常量
         */
        public static class Group {

            public final static String CRM_AUDIO_COMBINE_URL_UPDATE_GROUP = "CRM_AUDIO_COMBINE_URL_UPDATE_GROUP";


            /**
             * 检查有效客户消费组
             */
            public final static String CHECK_VALID_CUSTOMER_GROUP = "CHECK_VALID_CUSTOMER_GROUP";
            /**
             * 迁移无效客户消费组
             */
            public final static String TRANSFER_INVALID_CUSTOMER_GROUP = "TRANSFER_INVALID_CUSTOMER_GROUP";

            /**
             * 刷新客户名称和unCid group
             */
            public final static String CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_GROUP = "CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_GROUP";

            /**
             * 搜客宝相关标签同步消费组
             */
            public final static String CDP_CUS_FLAG_CUSTOMER_GROUP = "CDP_CUS_FLAG_CUSTOMER_GROUP";

            /**
             * 门户实例标签同步消费组
             */
            public final static String CDP_CUSTOMER_TAG_UPDATE_GROUP = "CDP_CUSTOMER_TAG_UPDATE_GROUP";

            /**
             * 成交客户保有流失消费组
             */
            public final static String SCRM_CUSTOMER_KEEP_AND_LOSS_GROUP = "SCRM_CUSTOMER_KEEP_AND_LOSS_GROUP";

            /**
             * 保有客户流转消费组 (已废弃)
             */
            public final static String SCRM_CUSTOMER_KEEP_CONVERT_GROUP = "SCRM_CUSTOMER_KEEP_CONVERT_GROUP";
            public final static String SCRM_CUSTOMER_KEEP_CONVERT_AI_GROUP = "SCRM_CUSTOMER_KEEP_CONVERT_AI_GROUP";
            public final static String SCRM_CUSTOMER_KEEP_CONVERT_AI_CHECK_GROUP = "SCRM_CUSTOMER_KEEP_CONVERT_AI_CHECK_GROUP";

            /**
             * 客户流失消费组
             */
            public final static String SCRM_CUSTOMER_LOSS_GROUP = "SCRM_CUSTOMER_LOSS_GROUP";

            /**
             * 保护关系 修复 消费组
             */
            public final static String SCRM_CUSTOMER_PROTECTED_FIX_GROUP = "SCRM_CUSTOMER_PROTECTED_FIX_GROUP";

            /**
             * 监听保护关系binlog同步cutomer数据
             */
            public final static String SCRM_CUSTOMER_PROTECTED_ASYNC_GROUP = "SCRM_CUSTOMER_PROTECTED_ASYNC_GROUP";
            public final static String SCRM_AI_ROBOT_GROUP = "SCRM_AI_ROBOT_GROUP";

            /**
             * 监听待流转流失表的binlog 消费组
             */
            public final static String CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_GROUP = "CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_GROUP";
            /**
             * 待流转流失表的id 消费组
             */
            public final static String CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_ID_GROUP = "CESUPPORT_SCRM_CUSTOMER_WILL_CIRCULATION_ID_GROUP";
            /**
             * 企业微信审批（回调到中企平台然后发MQ，我们监听）
             */
            public final static String SCRM_COOPERATION_APPROVE_GROUP = "SCRM_COOPERATION_APPROVE_GROUP";


            /**
             * 易企秀消息监听 消费group
             */
            public final static String SCRM_EQIXIU_MESSAGE_NOTIFY_GROUP = "SCRM_EQIXIU_MESSAGE_NOTIFY_GROUP";
            public final static String SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_GROUP = "SCRM_EQIXIU_MESSAGE_COUPON_NOTIFY_GROUP";

            /**
             * 三方活动上报 消费group
             */
            public final static String SCRM_CAMPAIGN_REPORT_GROUP = "SCRM_CAMPAIGN_REPORT_GROUP";

            public final static String CDP_SEGMENT_CUSTOMER_INFO_GROUP = "CDP_GROUP_CUSTOMER_INFO_GROUP";

            /**
             * 案例分享group
             */
            public static final String CDP_GROUP_WEB_SITE_CASE_CREATE_GROUP = "CDP_GROUP_WEB_SITE_CASE_CREATE_GROUP";

            /**
             * 案例分享group，用于更新网站的百度收录数、谷歌收录数
             */
            public static final String CDP_GROUP_WEB_SITE_CASE_INCLUDE_GROUP = "CDP_GROUP_WEB_SITE_CASE_INCLUDE_GROUP";

            public final static String CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_GROUP = "CDP_GROUP_CUSTOMER_DISTRIBUTE_STATUS_GROUP";

            /**
             * 商务拜访记录消费group
             */
            public static final String CESUPPORT_CRM_SITE_CLOCK_GROUP = "CESUPPORT_CRM_SITE_CLOCK_GROUP";

            /**
             * 分群客户保护关系消费group
             */
            public static final String SCRM_CUSTOMER_GROUP_PROTECTED_INFO_GROUP = "SCRM_CUSTOMER_GROUP_PROTECTED_INFO_GROUP";
            public static final String SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_GROUP = "SCRM_KEXF_MESSAGE_PPT_AI_NOTIFY_GROUP";
            public static final String SCRM_VOICE_AI_NOTIFY_GROUP = "SCRM_VOICE_AI_NOTIFY_GROUP";
            public static final String SCRM_SPEECH_RECOGNITION_GROUP = "SCRM_SPEECH_RECOGNITION_GROUP";
            public static final String SCRM_VOICE_AI_NOTIFY_FOLLOW_GROUP = "SCRM_VOICE_AI_NOTIFY_FOLLOW_GROUP";
            public final static String CESUPPORT_SCRM_TRADE_PRODUCT_AI_GROUP = "CESUPPORT_SCRM_TRADE_PRODUCT_AI_GROUP";

            public static final String SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_GROUP = "SCRM_KEXF_MESSAGE_PPT_AI_NEW_NOTIFY_GROUP";

            /**
             * 保护关系发生变更 同步最新的customer数据到保护关系表
             */
            public static final String SCRM_MAINTAIN_PROTECTED_INFO_GROUP = "SCRM_MAINTAIN_PROTECTED_INFO_GROUP";

            /**
             * 客户表发生变更 同步最新的customer数据到保护关系表
             */
            public static final String SCRM_MAINTAIN_PROTECTED_INFO_2_GROUP = "SCRM_MAINTAIN_PROTECTED_INFO_2_GROUP";

            /**
             * 新员工入职 处理
             */
            public static final String SCRM_ADD_NEW_EMPLOYEE_GROUP = "SCRM_ADD_NEW_EMPLOYEE_GROUP";

            /**
             * 客户流转
             */
            public static final String SCRM_CUSTOMER_CIRCULATION_GROUP = "SCRM_CUSTOMER_CIRCULATION_GROUP";

            /**
             * 客户开始流转
             */
            public static final String SCRM_CUSTOMER_CIRCULATION_START_GROUP = "SCRM_CUSTOMER_CIRCULATION_START_GROUP";

            public static final String SCRM_QYWEHCAT_SEND_MESSAGE_GROUP = "SCRM_QYWEHCAT_SEND_MESSAGE_GROUP";


            public final static String SCRM_AI_IMAGE_GENERATOR_GROUP = "SCRM_AI_IMAGE_GENERATOR_GROUP";
            public final static String SCRM_AI_WEBSITE_GENERATOR_GROUP = "SCRM_AI_WEBSITE_GENERATOR_GROUP";

            /**
             * abm 审核通过
             */
            public final static String CRM_ABM_REVIEW_PASS_GROUP = "CRM_ABM_REVIEW_PASS_GROUP";

	        /**
	         * CRM 现有渠道 leads导入 group
	         */
            public final static String SCRM_SEND_CLUE_NEW_GROUP = "SCRM_SEND_CLUE_NEW_GROUP";

            public final static String SCRM_WEB_SITE_CASE_ITEM_IMAGE_GROUP = "SCRM_WEB_SITE_CASE_ITEM_IMAGE_GROUP";
            public final static String SCRM_WEB_SITE_CASE_ITEM_IMAGE_ANALYSIS_GROUP = "SCRM_WEB_SITE_CASE_ITEM_IMAGE_ANALYSIS_GROUP";

        }

        /**
         * 顺序消费常量
         */
        public static class OrderlyConsumer {
            /**
             * 保证顺序消费的同一字符串
             */
            public final static String ORDER_FLAG = "SCRM-CENTER";
        }

        /**
         * 延迟消息级别
         * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
         */
        public static class Level {

            public static final int ONE_SECOND = 1;

            public static final int FIVE_SECOND = 2;

            public static final int TEN_SECOND = 3;

            public static final int THIRTY_SECOND = 4;

            public static final int ONE_MINUTE = 5;

            public static final int TWO_MINUTE = 6;

            public static final int THREE_MINUTE = 7;

            public static final int FOUR_MINUTE = 8;

            public static final int FIVE_MINUTE = 9;

            public static final int SIX_MINUTE = 10;

            public static final int SEVEN_MINUTE = 11;

            public static final int EIGHT_MINUTE = 12;

            public static final int NINE_MINUTE = 13;

            public static final int TEN_MINUTE = 14;

            public static final int TWENTY_MINUTE = 15;

            public static final int THIRTY_MINUTE = 16;

            public static final int ONE_HOUR = 17;

            public static final int TWO_HOUR = 18;

        }
    }
}
