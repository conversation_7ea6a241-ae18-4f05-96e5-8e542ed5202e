<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>scrm-center</artifactId>
        <groupId>com.ce.scrm</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>center-web</artifactId>
    <name>center-web</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <jvm.min>1024m</jvm.min>
        <jvm.max>2048m</jvm.max>
        <jvm.MaxDirectMemorySize>128m</jvm.MaxDirectMemorySize>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ce.scrm.center</groupId>
            <artifactId>center-dubbo-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-gj-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.ce.cesupport</groupId>
                    <artifactId>framework-cesupport-mongo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm</groupId>
            <artifactId>center-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce</groupId>
            <artifactId>sso-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>scrm-common-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>scrm-common-enums</artifactId>
        </dependency>
        <!-- Jsoup: HTML 解析 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>3.141.59</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>1.25</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-parsers</artifactId>
            <version>1.25</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>2048m</jvm.max>
                <jvm.MaxDirectMemorySize>128m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
    </profiles>


    <build>
        <finalName>center-web</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <configuration>
                            <finalName>${project.build.finalName}</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <descriptors>${project.basedir}/src/main/assembly/assembly.xml</descriptors>
                        </configuration>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
