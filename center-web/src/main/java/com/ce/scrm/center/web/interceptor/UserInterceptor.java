package com.ce.scrm.center.web.interceptor;

import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.router.RouterContext;
import com.ce.scrm.center.service.third.entity.view.EmployeeDataThirdView;
import com.ce.scrm.center.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.aspect.WebAspect;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * @version 1.0
 * @Description: 用户拦截器
 * @Author: lijinpeng
 * @Date: 2024/12/11 11:57
 */
@Component
@Slf4j
public class UserInterceptor implements HandlerInterceptor {

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private WebAspect webAspect;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		if (request.getRequestURI().contains("customer/import")) { // 临时测试跳过登录验证
			return true;
		}
        String empId = getEmpId(request);
        if(empId == null) {
            StringBuffer requestURL = request.getRequestURL();
            log.info("{}，没有empId", requestURL.toString());
            return true;
        }
        Optional<EmployeeDataThirdView> employeeData = employeeThirdService.getEmployeeData(empId);
        if(employeeData.isPresent()) {
            EmployeeDataThirdView employeeDataThirdView = employeeData.get();
            // 统一类型
            EmployeeInfoBusinessDto employeeInfoBusinessDto = BeanCopyUtils.convertToVo(employeeDataThirdView, EmployeeInfoBusinessDto.class);
            RouterContext.setCurrentUser(employeeInfoBusinessDto);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RouterContext.clear();
    }

    private String getEmpId(HttpServletRequest request) {
        try {
            WebResult<String> empIdResult = webAspect.checkPcLogin(request);
            if (!empIdResult.checkSuccess() || StringUtils.isBlank(empIdResult.getData())) {
                return null;
            }
            return empIdResult.getData();
        }catch (Exception e) {
            return null;
        }
    }

}
