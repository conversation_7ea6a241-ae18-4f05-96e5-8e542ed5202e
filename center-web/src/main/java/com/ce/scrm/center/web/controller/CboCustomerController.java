package com.ce.scrm.center.web.controller;

import cn.ce.cesupport.base.vo.ScrmResult;
import cn.ce.cesupport.enums.CodeMessageEnum;
import cn.ce.cesupport.enums.CustomerCreateWayEnum;
import cn.ce.cesupport.enums.CustomerTypeEnum;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dao.entity.CustomerLossLog;
import com.ce.scrm.center.dao.service.CustomerLossLogService;
import com.ce.scrm.center.dubbo.api.CboCustomerDubbo;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboCustomerPayDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CboProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView;
import com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView;
import com.ce.scrm.center.service.business.CboCustomerBusiness;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cbo.CboCreateCustomerBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.cbo.CboSaveContactPersonBusinessDto;
import com.ce.scrm.center.service.business.entity.view.cbo.CboCreateCustomerBusinessView;
import com.ce.scrm.center.service.business.entity.view.cbo.CboSaveContactPersonBusinessView;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetailByNameView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.web.entity.dto.CboCreateCustomerWebDto;
import com.ce.scrm.center.web.entity.dto.CboSaveContactPersonWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.CompanyInfoEsWebView;
import com.ce.scrm.center.web.util.SignatureUtils;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.view.CompanyInfoDubboView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 跨境专用
 * <AUTHOR>
 * @description
 * @date 2024/11/14
 */

@Slf4j
@RestController
@RequestMapping("/cbo")
public class CboCustomerController {

    @DubboReference(group = "scrm-center-api", version = "1.0.0", check = false)
    private CboCustomerDubbo cboCustomerDubbo;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ICompanyInfoEsDubbo iCompanyInfoEsDubbo;

    @Value("${third.customer.secret}")
    private String secret;

    @Resource
    private CustomerLossLogService customerLossLogService;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    private CboCustomerBusiness cboCustomerBusiness;

    /***
     * 保护关系查询
     * @param cboProtectDubboDto
     * <AUTHOR>
     * @date 2024/11/14 17:46
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<com.ce.scrm.center.dubbo.entity.response.DubboPageInfo<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>>
     **/
    @PostMapping("/getProtectByCondition")
    public WebResult<WebPageInfo<CboProtectDubboView>> getProtectByCondition(@RequestBody CboProtectDubboDto cboProtectDubboDto, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(cboProtectDubboDto.getSalerId(), secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<DubboPageInfo<CboProtectDubboView>> protectByCondition = cboCustomerDubbo.getProtectByCondition(cboProtectDubboDto);
        WebPageInfo<CboProtectDubboView> webPageInfo = BeanUtil.copyProperties(protectByCondition.getData(), WebPageInfo.class);
        if (protectByCondition.checkSuccess() && protectByCondition.getData()!=null){
            webPageInfo.setList(protectByCondition.getData().getList());
        }
        return WebResult.success(webPageInfo);
    }

    /**
     * 根据custId查询流失客户信息
     * @param customerId
     * @param sign
     * @return
     */
    @GetMapping("/getCustomerLossDateList")
    public WebResult<List<Date>> getCustomerLossDateList(@RequestParam(value = "customerId") String customerId, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        List<CustomerLossLog> list = customerLossLogService.lambdaQuery().eq(CustomerLossLog::getCustId, customerId).list();
        List<Date> collect = list.stream().map(CustomerLossLog::getPreDate).collect(Collectors.toList());
        return WebResult.success(collect);
    }

    /***
     * 联系人查询
     * @param customerId
     * <AUTHOR>
     * @date 2024/11/14 17:47
     * @version 1.0.0
     * @return com.ce.scrm.center.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.center.dubbo.entity.view.CboContactPersonDubboView>>
     **/
    @GetMapping("/getContactPersonByCustomerId/{customerId}")
    public WebResult<List<CboContactPersonDubboView>> getContactPersonByCondition(@PathVariable("customerId") String customerId,@RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<List<CboContactPersonDubboView>> contactPersonByCondition = cboCustomerDubbo.getContactPersonByCondition(customerId);
        WebResult<List<CboContactPersonDubboView>> webPageInfo = BeanUtil.copyProperties(contactPersonByCondition, WebResult.class);
        return webPageInfo;
    }


    /***
     * 根据客户ID查询保护关系
     * @param customerId
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    @GetMapping("/getProtectByCustomerId/{customerId}")
    public WebResult<CboProtectDubboView> getProtectByCustomerId(@PathVariable("customerId") String customerId, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerId, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        CboProtectDubboDto cboProtectDubboDto = new CboProtectDubboDto();
        cboProtectDubboDto.setCustomerId(customerId);
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getProtectByCustomerId(cboProtectDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }

    /***
     * 根据客户名称查询保护关系
     * @param customerName
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
     **/
    @GetMapping("/getProtectByCustomerName/{customerName}")
    public WebResult<CboProtectDubboView> getProtectByCustomerName(@PathVariable("customerName") String customerName, @RequestHeader("sign") String sign){
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerName, secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        CboProtectDubboDto cboProtectDubboDto = new CboProtectDubboDto();
        cboProtectDubboDto.setCustomerName(customerName);
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getProtectByCustomerName(cboProtectDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }

    /***
     * 根据客户名称查询客户信息
     * @param cboCustomerDubboDto
     * @param sign
     * <AUTHOR>
     * @date 2024/12/9 10:58
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
     **/
    @PostMapping("/getCustomerInfoByCondition")
    public WebResult<CboProtectDubboView> getCustomerInfoByCondition(@RequestBody CboCustomerDubboDto cboCustomerDubboDto, @RequestHeader("sign") String sign){
        if (cboCustomerDubboDto==null || (StringUtils.isBlank(cboCustomerDubboDto.getCustomerId()) && StringUtils.isBlank(cboCustomerDubboDto.getCustomerName()))){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (StringUtils.isNotBlank(cboCustomerDubboDto.getCustomerId())){
            String expectedSignature = SignatureUtils.generateSignature(cboCustomerDubboDto.getCustomerId(), secret);
            if (!Objects.equals(expectedSignature,sign)){
                return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
            }
        }else {
            if (StringUtils.isNotBlank(cboCustomerDubboDto.getCustomerName())) {
                String expectedSignature = SignatureUtils.generateSignature(cboCustomerDubboDto.getCustomerName(), secret);
                if (!Objects.equals(expectedSignature, sign)) {
                    return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
                }
            }
        }
        DubboResult<CboProtectDubboView> dubboResult = cboCustomerDubbo.getCustomerInfoByCondition(cboCustomerDubboDto);
        WebResult<CboProtectDubboView> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }


    /***
     * 支付完成
     * @param cboCustomerPayDubboDto
     * @param sign
     * <AUTHOR>
     * @date 2024/12/17 09:13
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.dubbo.entity.view.CboProtectDubboView>
    **/
    @PostMapping("/orderPay")
    public WebResult<Boolean> orderPay(@RequestBody CboCustomerPayDubboDto cboCustomerPayDubboDto, @RequestHeader("sign") String sign){
        if (cboCustomerPayDubboDto==null || StringUtils.isBlank(cboCustomerPayDubboDto.getCustomerId()) || StringUtils.isBlank(cboCustomerPayDubboDto.getSalerId()) || cboCustomerPayDubboDto.getPayTime()==null){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        if (StringUtils.isBlank(sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(cboCustomerPayDubboDto.getCustomerId(), secret);
        if (!Objects.equals(expectedSignature,sign)){
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        DubboResult<Boolean> dubboResult = cboCustomerDubbo.orderPay(cboCustomerPayDubboDto);
        WebResult<Boolean> webResult = BeanUtil.copyProperties(dubboResult, WebResult.class);
        return webResult;
    }

    /**
     * Description: 企业名称模糊查询，返回：  pid、企业名称列表
     * @author: JiuDD
     * @param customerName 企业名称
     * @param sign 签名，只对 customerName 进行签名
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List<com.ce.scrm.center.web.entity.view.CompanyInfoEsWebView>>
     * date: 2025/6/23 11:32
     */
    @GetMapping("/listByLikeName/{customerName}")
    public WebResult<List<CompanyInfoEsWebView>> listByLikeName(@PathVariable("customerName") String customerName, @RequestHeader("sign") String sign) {
        if (StringUtils.isBlank(sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerName, secret);
        if (!Objects.equals(expectedSignature, sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        List<CompanyInfoDubboView> dubboResult = iCompanyInfoEsDubbo.listByLikeName(customerName);
        if (dubboResult == null || dubboResult.isEmpty()) {
            return WebResult.success(Lists.newArrayList());
        }
        List<CompanyInfoEsWebView> collect = dubboResult.stream().map(item -> {
            CompanyInfoEsWebView webView = new CompanyInfoEsWebView();
            webView.setPid(item.getPid());
            webView.setEntName(item.getEntName());
            return webView;
        }).collect(Collectors.toList());
        return WebResult.success(collect);
    }

    /**
     * Description: 通过客户名称，准确查询CRM库。判断此此名称在CRM是否存在，如果存在返回 customerId
     * @author: JiuDD
     * @param customerName 企业名称
     * @param sign 签名，只对 customerName 进行签名
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     * date: 2025/6/23 14:32
     */
    @GetMapping("/getCustomerIdByName/{customerName}")
    public WebResult<String> getCustomerIdByName(@PathVariable("customerName") String customerName, @RequestHeader("sign") String sign) {
        if (StringUtils.isBlank(sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(customerName, secret);
        if (!Objects.equals(expectedSignature, sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        Optional<CustomerDataThirdView> customerDataByCustomerName = customerThirdService.getCustomerDataByCustomerName(customerName);
        return customerDataByCustomerName.map(customerDataThirdView -> WebResult.success(customerDataThirdView.getCustomerId())).orElseGet(WebResult::success);
    }

    /**
     * Description: 输入pid，调搜客宝工商信息接口，返回工商信息
     * @author: JiuDD
     * @param pid 企业pid
     * @param sign 签名，只对 pid 进行签名
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List<com.ce.scrm.center.web.entity.view.CompanyInfoEsWebView>>
     * date: 2025/6/23 15:18
     */
    @GetMapping("/getCompanyDetailByPid/{pid}")
    public WebResult<BigDataCompanyDetailByNameView> getCompanyDetailByPid(@PathVariable("pid") String pid, @RequestHeader("sign") String sign) {
        if (StringUtils.isBlank(sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        String expectedSignature = SignatureUtils.generateSignature(pid, secret);
        if (!Objects.equals(expectedSignature, sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        BigDataCompanyDetailByNameView companyDetailByPid = bigDataThirdService.getCompanyDetailByPid(pid);
        return WebResult.success(companyDetailByPid);
    }

    /**
     * Description: 添加客户。客户存在时直接返回客户ID，不存在时添加客户并返回客户ID
     * @author: JiuDD
     * @param dto
     * @param sign 签名，只对 customerName 进行签名
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     * date: 2025/6/23 19:34
     */
    @PostMapping("/cboAddCustomer")
    public WebResult<String> cboAddCustomer(@RequestBody CboCreateCustomerWebDto dto, @RequestHeader("sign") String sign) {
        if (StringUtils.isBlank(sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (StringUtils.isBlank(dto.getCustomerName())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NAME_NULL);
        }
        String expectedSignature = SignatureUtils.generateSignature(dto.getCustomerName(), secret);
        if (!Objects.equals(expectedSignature, sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (Objects.isNull(dto.getCustomerType())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CUSTOMER_TYPE_NULL);
        }
        if (Objects.isNull(CustomerTypeEnum.getCustomerTypeByCode(dto.getCustomerType()))) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CUSTOMER_TYPE_ERROR);
        }
        if (Objects.equals(dto.getCustomerType(), CustomerTypeEnum.COMPANY.getCode())) {
            String custNameRegex = "([\\u4E00-\\u9FA5]|\\（|\\）|[0-9]|〇|《|》|—|、){5,100}";
            // 校验客户名称
            if (!dto.getCustomerName().matches(custNameRegex)) {
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_COMPANY_NAME_ERROR);
            }
        }
        if (Objects.equals(dto.getCustomerType(), CustomerTypeEnum.PERSONAL.getCode())) {
            if (Objects.isNull(dto.getCertificateType())) {
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CERTIFICATE_TYPE_NULL);
            }
            if (StringUtils.isBlank(dto.getCertificateCode())) {
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CERTIFICATE_CODE_NULL);
            }
        }
        if (StringUtils.isBlank(dto.getCreator())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CREATOR_NULL);
        }
        //写死创建方式为 跨境会员
        dto.setCreateWay(CustomerCreateWayEnum.CBO_MEMBER.getCode());
        CboCreateCustomerBusinessDto businessDto = BeanUtil.copyProperties(dto, CboCreateCustomerBusinessDto.class);
        CboCreateCustomerBusinessView businessView = cboCustomerBusiness.cboAddCustomer(businessDto);
        if (StringUtils.isNotBlank(businessView.getErrMsg())) {
            return WebResult.error(WebCodeMessageEnum.ADD_CUSTOMER_FAIL.getCode(), businessView.getErrMsg());
        }
        return WebResult.success(businessView.getCustomerId());
    }

    /**
     * Description: 添加联系人
     * @author: JiuDD
     * @param dto 联系人信息
     * @param sign 签名，只对 customerId 进行签名
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.String>
     * date: 2025/7/1 20:01
     */
    @PostMapping(value = "/cboSaveContactPerson")
    public WebResult<String> cboSaveContactPerson(@RequestBody CboSaveContactPersonWebDto dto, @RequestHeader("sign") String sign) {
        if (StringUtils.isBlank(sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (StringUtils.isBlank(dto.getCustomerId())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CUSTOMER_ID_NULL);
        }
        String expectedSignature = SignatureUtils.generateSignature(dto.getCustomerId(), secret);
        if (!Objects.equals(expectedSignature, sign)) {
            return WebResult.error(WebCodeMessageEnum.SIGN_ERROR.getCode(), WebCodeMessageEnum.SIGN_ERROR.getMsg());
        }
        if (StringUtils.isBlank(dto.getContactPersonName())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CONTACT_PERSON_NAME_NULL);
        }
        if (Objects.isNull(dto.getGender())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_GENDER_NULL);
        }
        if (StringUtils.isBlank(dto.getPosition())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_POSITION_NULL);
        }
        if (StringUtils.isBlank(dto.getPhone())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_PHONE_NULL);
        }
        if (StringUtils.isBlank(dto.getOperator())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_CREATOR_NULL);
        }
        CboSaveContactPersonBusinessDto businessDto = BeanUtil.copyProperties(dto, CboSaveContactPersonBusinessDto.class);
        CboSaveContactPersonBusinessView businessView = cboCustomerBusiness.cboSaveContactPerson(businessDto);
        if (StringUtils.isNotBlank(businessView.getErrMsg())) {
            return WebResult.error(WebCodeMessageEnum.ADD_CONTACT_FAIL.getCode(), businessView.getErrMsg());
        }
        return WebResult.success(businessView.getContactPersonId());
    }
}
