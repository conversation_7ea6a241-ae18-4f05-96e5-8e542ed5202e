package com.ce.scrm.center.web.controller.customer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.TurnoverCustomerBusiness;
import com.ce.scrm.center.service.business.entity.dto.TurnoverFollowUpClueOldCustPageBusinessDto;
import com.ce.scrm.center.service.business.entity.view.customer.TurnoverCustomerOldCustPageBusinessView;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.TurnoverFollowUpClueForOldCustWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.TurnoverCustomerOldCluePageWebView;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @description 成交客户
 * <AUTHOR>
 * @date 2025-01-23 11:02
 */
@RestController
@Login
@RequestMapping("turnover")
@Slf4j
@AllArgsConstructor
@SuppressWarnings("unused")
public class TurnoverCustomerController {

	private final TurnoverCustomerBusiness turnoverCustomerBusiness;

	/**
	 * @Description 成交客户-成交客户-老客户线索跟进
	 * 迁移自老项目：TurnoverCustController#getYYGJCustPage
	 * <AUTHOR>
	 * @date 2025/02/05 11:50
	 * @param req {@link TurnoverFollowUpClueForOldCustWebDto}
	 * @return {@link WebResult}
	 */
	@PostMapping("followUpClueForOldCust")
	public WebResult<WebPageInfo<TurnoverCustomerOldCluePageWebView>> followUpClueForOldCust(@RequestBody @Valid TurnoverFollowUpClueForOldCustWebDto req) {
		Page<TurnoverCustomerOldCustPageBusinessView> pageBusiness = turnoverCustomerBusiness.followUpClueForCustPage(BeanCopyUtils.convertToVo(req, TurnoverFollowUpClueOldCustPageBusinessDto.class));
		WebPageInfo<TurnoverCustomerOldCluePageWebView> pageConversion = WebPageInfo.pageConversion(pageBusiness, TurnoverCustomerOldCluePageWebView.class);
		return WebResult.success(pageConversion);
	}
}
