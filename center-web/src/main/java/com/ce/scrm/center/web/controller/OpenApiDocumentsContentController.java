//package com.ce.scrm.center.web.controller;
//
//import cn.ce.cesupport.emp.service.EmployeeAppService;
//import cn.ce.cesupport.emp.vo.EmployeeVo;
//import cn.ce.cesupport.framework.base.vo.MapResultBean;
//import cn.ce.cesupport.framework.base.vo.Pagination;
//import cn.ce.cesupport.gj.service.DocumentAppservice;
//import cn.ce.cesupport.gj.service.DocumentsContentAppService;
//import cn.ce.cesupport.gj.service.DocumentsContentTextAppService;
//import cn.ce.cesupport.gj.vo.DocumentClassifyVO;
//import cn.ce.cesupport.gj.vo.DocumentsContentTextVo;
//import cn.ce.cesupport.gj.vo.DocumentsContentVo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @作者：<EMAIL>
// * @描述：开放接口-内容中心（提供给营销平台）
// * @日期：2024/12/13 09:28
// */
//@Slf4j
//@RestController
//@RequestMapping(value = "/openapi/documentsContent")
//public class OpenApiDocumentsContentController {
//
//
//    @DubboReference(group = "ceSupportCenter", check = false)
//    private DocumentsContentAppService documentsContentAppService;
//
//    @DubboReference(group = "ceSupportCenter", check = false)
//    private DocumentsContentTextAppService documentsContentTextAppService;
//
//    @DubboReference(group = "ceSupportCenter", check = false)
//    private DocumentAppservice documentAppservice;
//
//    @DubboReference
//    private EmployeeAppService employeeAppService;
//
//
//    /**
//     * @作者：<EMAIL>
//     * @描述：文档列表(可见范围)
//     * @日期：2024/12/11 14:04
//     * @参数：[empId, requestParams]
//     * @返回：cn.ce.cesupport.framework.base.vo.MapResultBean
//     */
//    @GetMapping(value = "/findDocumentContentListByPage")
//    public MapResultBean findDocumentContentListByPage(@RequestParam(name = "empId") String empId,
//                                                       @RequestParam(required = false, defaultValue = "1") Integer currentPage,
//                                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
//        MapResultBean result = new MapResultBean(101, "成功");
//        if (StringUtils.isEmpty(empId)) {
//            return new MapResultBean(301, "参数为空！");
//        }
//        EmployeeVo employeeVo = employeeAppService.selectOneNoPwd(empId);
//        if (employeeVo == null) {
//            return new MapResultBean(201, "无此员工" + empId);
//        }
//        Pagination<DocumentsContentVo> pagination = new Pagination<>();
//        pagination.setCurrentPage(currentPage);
//        pagination.setPageSize(pageSize);
//        Map<String, Object> map = new HashMap<>();
//        pagination.setParams(map);
//        try {
//            Pagination<DocumentsContentVo> documentsContentVoPagination = documentsContentAppService.selectDocumentsContentPermissionByPage(pagination, employeeVo.getOrgId());
//            result.setData(documentsContentVoPagination.getPageMap());
//        } catch (Exception e) {
//            log.error("异常：", e);
//            return new MapResultBean(500, "异常！");
//        }
//        return result;
//    }
//
//    /**
//     * @作者：<EMAIL>
//     * @描述：分类初始化
//     * @日期：2024/12/11 14:04
//     * @参数：[empId, requestParams]
//     * @返回：cn.ce.cesupport.framework.base.vo.MapResultBean
//     */
//    @GetMapping(value = "/queryClassify")
//    public MapResultBean queryClassify() {
//        MapResultBean result = new MapResultBean(101, "成功");
//        try {
//            DocumentClassifyVO documentClassifyVO = new DocumentClassifyVO();
//            documentClassifyVO.setClassifyType(4);
//            List<DocumentClassifyVO> documentClassifyVOS = documentAppservice.selectAllClassify(documentClassifyVO);
//            if (!CollectionUtils.isEmpty(documentClassifyVOS)) {
//                Map<String, Object> map = new HashMap<>();
//                map.put("data", documentClassifyVOS);
//                result.setData(map);
//            }
//        } catch (Exception e) {
//            log.error("异常：", e);
//            return new MapResultBean(500, "异常！");
//        }
//        return result;
//    }
//
//    /**
//     * @作者：<EMAIL>
//     * @描述：获取富文本
//     * @日期：2024/12/11 14:04
//     * @参数：[empId, requestParams]
//     * @返回：cn.ce.cesupport.framework.base.vo.MapResultBean
//     */
//    @GetMapping(value = "/findDocumentContentText")
//    public MapResultBean findDocumentContentText(@RequestParam(name = "documentId") Integer documentId) {
//        MapResultBean result = new MapResultBean(101, "成功");
//        if (documentId == null) {
//            return new MapResultBean(301, "参数为空！");
//        }
//        try {
//            Map<String, Object> map = new HashMap<>();
//            DocumentsContentTextVo documentsContentTextVo = documentsContentTextAppService.selectByDocumentId(documentId);
//            map.put("info", documentsContentTextVo);
//            result.setData(map);
//        } catch (Exception e) {
//            log.error("异常：", e);
//            return new MapResultBean(500, "异常！");
//        }
//        return result;
//    }
//}
//
//
//
//
