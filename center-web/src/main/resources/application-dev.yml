spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************
    username: MYSQL(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: epRedis@019
    cluster:
      nodes: ************
dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

sequence:
  zkAddress: ************
  zkPort: 2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-center-job-pre
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      port: 9353
      oneTimesJob:
        timeout: 10000

#业务平台
#rocketmq:
#  name-server: ************:9876
#  producer:
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b
#  consumer:
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b

#商城
rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

mq:
  prefix: TEST_

nacos:
  config:
    server-addr: ************:8848
    username: NACOS(cIJ2K2dWtlO7IGs8Ui0KJA==)
    password: NACOS(hUNsVRXSd3byOedVM1Q9Aqs0scX3BBGQwp+gyzItewg=)
    namespace: test1

ce:
  sso:
    client:
      ssoServer: https://test-passportceboss.300.cn/CAS
      redisHosts: ************:7000,************:7001,************:7002,************:7003,************:7004,************:7005
      redisPassword: LyjB7]vUyoZ
      clientDomain: https://test-scrm.ceboss.cn
      name: scrmSsoFilter
sso:
  excludedPaths: /entwx/*

logging:
  #  config: classpath:logback-pre.xml
  level:
    com.ce.scrm.center.service.business: debug
    com.ce.scrm.center.dao.mapper.EqixiuDataResultMapper: debug
    com.ce.scrm.center.dao.mapper.EqixiuLotteryDetailMapper: debug
    com.ce.scrm.center.dao.mapper.EqixiuDataReceiverOriginMapper: debug
    com.ce.scrm.center.dao.mapper.EqixiuActivityInfoMapper: debug
    com.ce.scrm.center.dao.mapper.EqixiuActivityShareInfoMapper: debug
    com.ce.scrm.center.dao.service.impl.EqixiuActivityInfoServiceImpl: debug
    com.ce.scrm.center.dao.service.impl.EqixiuActivityShareInfoServiceImpl: debug
    com.ce.scrm.center.dao.service.impl.EqixiuDataReceiverOriginServiceImpl: debug
    com.ce.scrm.center.dao.service.impl.EqixiuDataResultServiceImpl: debug
    com.ce.scrm.center.dao.service.impl.EqixiuLotteryDetailServiceImpl: debug
    com.ce.scrm.center.dao.service.IEqixiuActivityShareInfoService: debug
    com.ce.scrm.center.dao.service.IEqixiuDataResultService: debug
    com.ce.scrm.center.dao.service.IEqixiuLotteryDetailService: debug
    com.ce.scrm.center.dao.service.IEqixiuActivityInfoService: debug
    com.ce.scrm.center.dao.service.IEqixiuDataReceiverOriginService: debug
    org.springframework: warn
    org.apache.ibatis.logging: debug
    cn.apache.dubbo: error
    org.apache: error
    cn.ce.common.redis.switcher: error

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d199f33a-949e-4370-8eee-c573a51d185e

bigdata:
  enterpriseInfo: https://gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

third:
  customer:
    secret: 31b9763b659c49489d348c2e6a261a64
    secretCbo: bc6d4b8d1956456d929f672c0746b5aa
  file:
    url: http://test-cesupport-images.ceboss.cn/upload

wx:
  isopen: 1

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl #开启sql日志

#易企秀
ecmp:
  signatureKey: ktWVfoqNCSxEQU47
  encodingKey: og9pVN52dCeCXoEFx0sTbXTamcVkhx7SMi2LX1MoLwe
  secretKey: qOdqk33y8F6mMankOrEUl5Vh3WUQM5io
  secretId: 5222YVS

auth:
  login:
    kuajing:
      secret: jhs_dnsadkas-0w3
    cesupport:
      secret: jhs_dnsadkas-0w3

notifyshareInfo: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=64a7d2da-f69c-4787-a446-fe7cce37fe9b

template:
  id: 3WMVSkMUJFKdR5uwcfgD8E9XnLmEBaPjGiJT1cBA


file:
  upload:
    dir: /data/share/www/offline/upload
    url: http://test-cesupport-images.ceboss.cn/upload
