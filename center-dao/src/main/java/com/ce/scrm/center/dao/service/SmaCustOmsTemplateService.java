package com.ce.scrm.center.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ce.scrm.center.dao.entity.CustOmsTemplate;
import com.ce.scrm.center.dao.entity.TurnoverCustOfOldCluePageQuery;

/**
 * CustOmsTemplate Service
 */
public interface SmaCustOmsTemplateService extends IService<CustOmsTemplate> {

	/**
	 * 成交客户-老客户线索跟进
	 * @param condition 查询条件
	 */
    Page<CustOmsTemplate> queryFollowUpClueOfOldCust(TurnoverCustOfOldCluePageQuery condition);

}
