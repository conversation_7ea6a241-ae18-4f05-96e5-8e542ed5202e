<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ce.scrm.center.dao.mapper.SmaCustOmsTemplateMapper" >


    <!-- 老客户线索跟进分页 -->
    <select id="queryFollowUpClueOfOldCust" parameterType="com.ce.scrm.center.dao.entity.TurnoverCustOfOldCluePageQuery"  resultType="com.ce.scrm.center.dao.entity.CustOmsTemplate">
        SELECT
        t.CUST_ID AS `CUST_ID`,
        t.ID AS `ID`,
        a.CUST_NAME AS `CUST_NAME`,
        a.CUST_TYPE AS `CUST_TYPE`,
        a.SALER_ID AS `SALER_ID`,
        a.BUSSDEPT_ID AS `BUSSDEPT_ID`,
        a.SUBCOMPANY_ID AS `SUBCOMPANY_ID`,
        a.AREA_ID AS `AREA_ID`,
        a.FOLLOW_OR_NOT AS `FOLLOW_OR_NOT`,
        a.VISIT_OR_NOT AS `VISIT_OR_NOT`,
        t.TEMPLATE_ISSUED_SUCCESS_TIME AS `TEMPLATE_ISSUED_SUCCESS_TIME`,
        t.TEMPLATE_NAME AS `TEMPLATE_NAME`,
        t.TEMPLATE_ID AS `TEMPLATE_ID`,
        t.TEMPLATE_TYPE AS `TEMPLATE_TYPE`,
        t.TEMPLATE_ISSUED_EXCEED_TIME AS `TEMPLATE_ISSUED_EXCEED_TIME`,
        CASE
        WHEN t.TEMPLATE_ISSUED_EXCEED_TIME <![CDATA[ >= ]]> DATE_FORMAT(NOW(), '%Y-%m-%d') THEN
        0 ELSE 1
        END AS IS_EXCEED
        from  (SELECT
        b.CUST_ID as CUST_ID ,
        c.CUST_NAME AS `CUST_NAME`,
        c.CUST_TYPE AS `CUST_TYPE`,
        c.SALER_ID AS `SALER_ID`,
        c.BUSSDEPT_ID AS `BUSSDEPT_ID`,
        c.SUBCOMPANY_ID AS `SUBCOMPANY_ID`,
        c.AREA_ID AS `AREA_ID`,
        v.FOLLOW_OR_NOT AS `FOLLOW_OR_NOT`,
        v.VISIT_OR_NOT AS `VISIT_OR_NOT`
        FROM
        T_CUST_OMS_TEMPLATE b
        LEFT JOIN CM_CUST_PROTECT c ON b.CUST_ID =c.CUST_ID
        LEFT JOIN CM_CUST_PROTECT_VIEW v ON b.CUST_ID = v.CUST_ID
        <where>
            <if test="params.salerId != null and params.salerId !='' " >
                AND c.SALER_ID = "${params.salerId}"
            </if>
            <if test="params.bussdeptId != null and params.bussdeptId !='' " >
                AND c.BUSSDEPT_ID = "${params.bussdeptId}"
            </if>
            <if test="params.subcompanyId != null and params.subcompanyId !='' " >
                AND c.SUBCOMPANY_ID = "${params.subcompanyId}"
            </if>
            <if test="params.areaId != null and params.areaId  !='' " >
                AND c.AREA_ID = "${params.areaId}"
            </if>
            <if test="params.custName != null and params.custName !='' " >
                AND c.CUST_NAME LIKE CONCAT('%', #{params.custName}, '%')
            </if>
            <if test="params.followOrNot != null" >
                AND v.FOLLOW_OR_NOT =${params.followOrNot}
            </if>
            <if test="params.visitOrNot != null" >
                AND v.VISIT_OR_NOT =${params.visitOrNot}
            </if>
            <if test="params.custId != null and params.custId  !='' " >
                AND b.CUST_ID = #{params.custId}
            </if>
            <if test="params.idList != null" >
                AND b.ID IN
                <foreach collection="params.idList" item="id" open="(" separator="," close=")">
                    #{id }
                </foreach>
            </if>
            <if test="params.templateName != null and params.templateName  !='' " >
                AND b.TEMPLATE_NAME = "${params.templateName}"
            </if>
        </where>
        GROUP BY
        CUST_ID
        order By b.TEMPLATE_ISSUED_SUCCESS_TIME DESC   LIMIT 0,500
        ) a
        LEFT JOIN  T_CUST_OMS_TEMPLATE t
        on  a.CUST_ID=t.CUST_ID
        LEFT JOIN (select t2.CUST_ID,MAX(t2.TEMPLATE_ISSUED_EXCEED_TIME) as `TEMPLATE_ISSUED_EXCEED_TIME` from T_CUST_OMS_TEMPLATE t2 GROUP BY t2.CUST_ID) t3
        on t3.cust_id=a.cust_id
        <where>
            <if test="1==1">
                and t.ID=((SELECT t1.id from  T_CUST_OMS_TEMPLATE  t1 where t1.CUST_ID=t.CUST_ID ORDER BY t1. TEMPLATE_ISSUED_SUCCESS_TIME desc LIMIT 1))
            </if>
            <if test="params.isExceed != null and params.isExceed ==1 ">
                AND t3.TEMPLATE_ISSUED_EXCEED_TIME <![CDATA[ < ]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
            </if>
            <if test="params.isExceed != null and params.isExceed ==0 ">
                AND t3.TEMPLATE_ISSUED_EXCEED_TIME <![CDATA[ >= ]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
            </if>
            <if test="params.orderBy != null and params.orderBy !='' " >
                order by ${params.orderBy}
            </if>
        </where>
    </select>
</mapper>